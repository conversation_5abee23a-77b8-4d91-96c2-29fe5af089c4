"use client";

import { useEffect, useState, useRef } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/stores/auth-store";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useDebounce } from "@/hooks/use-debounce";
import { PortfolioData } from "@/lib/types";
import { getPortfolio, updatePortfolio, deletePortfolio, uploadFile, generateUniqueSlug } from "@/lib/portfolio-api";
import { EditorProvider, useEditor } from "@/contexts/EditorContext";
import { isEqual } from 'lodash';

// UI and Theme Components
import { ProfolifyTheme } from "@/components/portfolio-themes/ProfolifyTheme";
import { ModernTheme } from "@/components/portfolio-themes/ModernTheme";
import { Loader2 } from "lucide-react";
import { ConfirmationDialog } from "@/components/ui/ConfirmationDialog";
import { PortfolioHeader } from "./components/PortfolioHeader";
import { ExportProvider } from "@/contexts/ExportContext";

function PortfolioEditorCore() {
  const { user } = useAuthStore();
  const queryClient = useQueryClient();
  const router = useRouter();
  const { state, dispatch } = useEditor();
  const { formData } = state;

  const debouncedFormData = useDebounce(formData, 1500);
  const serverStateRef = useRef<PortfolioData | null>(null);
  const [isDirty, setIsDirty] = useState(false);

  useEffect(() => {
    if (formData && !serverStateRef.current) {
      serverStateRef.current = formData;
    }
  }, [formData]);

  useEffect(() => {
    if (serverStateRef.current) {
      const hasChanges = !isEqual(formData, serverStateRef.current);
      setIsDirty(hasChanges);
    }
  }, [formData]);

  // --- MUTATIONS ---

  const { mutate: autoSave } = useMutation({
    mutationFn: (data: PortfolioData) => updatePortfolio({ userId: user!.uid, data }),
    onSuccess: () => {
      // Corrected: The ONLY job of a successful auto-save is to show the status.
      // It does NOT change isDirty or serverStateRef. This keeps the "Update Live"
      // button correctly enabled because the changes are saved but not yet published.
      dispatch({ type: 'SET_SAVE_STATUS', payload: 'saved' });
    },
    onError: () => dispatch({ type: 'SET_SAVE_STATUS', payload: 'error' }),
  });

  const { mutate: publishMutation, isPending: isPublishing } = useMutation({
    mutationFn: async (data: PortfolioData): Promise<PortfolioData> => {
      let dataToSave = { ...data, isPublished: true };
      const hasNameChanged = serverStateRef.current?.userName !== data.userName;
      const isDefaultSlug = data.slug === user!.uid;
      const needsNewUrl = (hasNameChanged || isDefaultSlug);

      if (needsNewUrl && data.userName) {
        toast.info("Generating your new public URL...");
        const newSlug = await generateUniqueSlug(data.userName, user!.uid);
        dataToSave.slug = newSlug;
      }

      await updatePortfolio({ userId: user!.uid, data: dataToSave });
      return dataToSave;
    },
    onSuccess: (savedData) => {
      toast.success("Portfolio published successfully!");
      queryClient.invalidateQueries({ queryKey: ['portfolio', user!.uid] });

      // Correct: Publishing is the ONLY action that makes the state "clean".
      serverStateRef.current = savedData;
      setIsDirty(false);

      router.push('/dashboard');
    },
    onError: (error) => {
      console.error("Publish failed:", error);
      toast.error("Failed to publish portfolio. Please try again.");
    },
  });

  const { mutate: deleteMutation, isPending: isDeleting } = useMutation({
    mutationFn: () => deletePortfolio(user!.uid),
    onSuccess: () => {
      queryClient.removeQueries({ queryKey: ['portfolio', user!.uid] });
      router.push('/choose-template');
      toast.success("Portfolio discarded. Choose a new template to start over.");
    },
    onError: () => toast.error("Failed to delete portfolio."),
  });

  const { mutate: uploadMutation } = useMutation({
    mutationFn: (vars: { file: File, type: 'profile' | 'resume' | 'project', id?: string }) => {
      dispatch({ type: 'SET_UPLOADING', payload: { type: vars.type, id: vars.id } });
      return uploadFile(vars.file);
    },
    onSuccess: (url, vars) => {
      // Add timestamp to force re-render and cache busting
      const urlWithTimestamp = url.includes('?') ? `${url}&t=${Date.now()}` : `${url}?t=${Date.now()}`;

      toast.success(`${vars.type.charAt(0).toUpperCase() + vars.type.slice(1)} uploaded successfully!`);

      if (vars.type === 'profile') {
        dispatch({ type: 'UPDATE_FIELD', payload: { field: 'profileImageUrl', value: urlWithTimestamp } });
      } else if (vars.type === 'resume') {
        dispatch({ type: 'UPDATE_FIELD', payload: { field: 'resumeUrl', value: url } });
      } else if (vars.type === 'project' && vars.id) {
        const projectIndex = formData.projects.findIndex(p => p.id === vars.id);
        if (projectIndex !== -1) {
          dispatch({ type: 'UPDATE_PROJECT', payload: { index: projectIndex, field: 'imageUrl', value: urlWithTimestamp } });
        }
      }
    },
    onError: (error) => {
      console.error('Upload error:', error);
      toast.error("Upload failed. Please try again.");
    },
    onSettled: () => {
      dispatch({ type: 'SET_UPLOADING', payload: null });
    },
  });

  // --- Auto-save effect ---
  useEffect(() => {
    // The auto-save draft functionality.
    if (isDirty) {
      dispatch({ type: 'SET_SAVE_STATUS', payload: 'saving' });
      autoSave(debouncedFormData);
    }
  }, [debouncedFormData, autoSave, isDirty]);

  // --- Handlers & State ---
  const [dialog, setDialog] = useState({ isOpen: false, onConfirm: () => { } });
  const handlePreview = () => { localStorage.setItem('portfolio-preview', JSON.stringify(formData)); window.open('/portfolio/preview', '_blank'); };
  const handleDeleteConfirm = () => setDialog({ isOpen: true, onConfirm: () => deleteMutation() });

  return (
    <>
      <div className="h-full flex flex-col bg-muted/20">
        <header className="p-4 border-b bg-background flex justify-between items-center sticky top-0 z-20">
          <PortfolioHeader
            isPublishing={isPublishing}
            isDeleting={isDeleting}
            isDirty={isDirty}
            onPreview={handlePreview}
            onDelete={handleDeleteConfirm}
            onTogglePublish={() => publishMutation(formData)}
          />
        </header>
        <main className="flex-1 overflow-y-auto">
          {formData.templateId === 'profolify-theme-v1' && (
            <ProfolifyTheme isEditing={true} onImageUpload={uploadMutation} />
          )}
          {formData.templateId === 'modern-theme-v1' && (
            <ModernTheme isEditing={true} onImageUpload={uploadMutation} />
          )}
        </main>
      </div>
      <ConfirmationDialog
        isOpen={dialog.isOpen}
        onClose={() => setDialog({ isOpen: false, onConfirm: () => { } })}
        onConfirm={() => { dialog.onConfirm(); setDialog({ isOpen: false, onConfirm: () => { } }); }}
        title="Are you absolutely sure?"
        description="This action is irreversible and will permanently delete all of your portfolio data."
      />
    </>
  );
}

// The page wrapper that fetches data and provides the context
export default function PortfolioPage() {
  const { user } = useAuthStore();
  const router = useRouter();

  const { data: initialData, isLoading } = useQuery<PortfolioData | null>({
    queryKey: ["portfolio", user!.uid],
    queryFn: () => getPortfolio(user!.uid),
    enabled: !!user,
    staleTime: 5 * 60 * 1000,
  });

  useEffect(() => {
    if (!isLoading && !initialData) {
      toast.error("No portfolio found. Please select a template to begin.");
      router.replace('/choose-template');
    }
  }, [isLoading, initialData, router]);

  if (isLoading || !initialData) {
    return <div className="flex h-full items-center justify-center"><Loader2 className="h-8 w-8 animate-spin" /></div>;
  }

  return (
    <ExportProvider value={false}>
      <EditorProvider initialData={initialData}>
        <PortfolioEditorCore />
      </EditorProvider>
    </ExportProvider>
  );
}