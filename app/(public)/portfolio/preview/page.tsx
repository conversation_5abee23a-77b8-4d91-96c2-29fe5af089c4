"use client";
import { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Smartphone, Tablet, Monitor } from "lucide-react";
import { cn } from "@/lib/utils";
import { PortfolioData } from "@/lib/types";

// --- NEW: Import all possible theme components ---
import { ProfolifyTheme } from "@/components/portfolio-themes/ProfolifyTheme";
import { ModernTheme } from "@/components/portfolio-themes/ModernTheme";

export default function PortfolioPreviewPage() {
  const [data, setData] = useState<PortfolioData | null>(null);
  const [view, setView] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');

  useEffect(() => {
    // 1. Get the preview data from localStorage (no change here)
    const previewData = localStorage.getItem('portfolio-preview');
    if (previewData) {
      setData(JSON.parse(previewData));
    }
  }, []);

  // Show a loader while data is being loaded from localStorage
  if (!data) {
    return <div className="flex h-screen items-center justify-center bg-muted">Loading Preview...</div>;
  }

  const viewClasses = {
    desktop: 'w-full h-full',
    tablet: 'w-[768px] h-[90%] rounded-lg shadow-2xl', // Exact tablet breakpoint
    mobile: 'w-[375px] h-[90%] rounded-lg shadow-2xl', // Standard mobile width
  };

  const renderTheme = () => {
    // 2. --- FIX: Use a switch statement to render the correct theme ---
    // This logic checks the templateId from the data we got from localStorage.
    switch (data.templateId) {
      case 'modern-theme-v1':
        return <ModernTheme isEditing={false} serverData={data} />;

      case 'profolify-theme-v1':
      default:
        return <ProfolifyTheme isEditing={false} serverData={data} />;
    }
  };

  return (
    <>
      {/* Custom CSS for responsive preview simulation */}
      <style dangerouslySetInnerHTML={{
        __html: `
          /* Mobile preview - force all responsive classes to mobile state */
          .preview-mobile .sm\\:grid-cols-2 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
          .preview-mobile .sm\\:grid-cols-3 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
          .preview-mobile .lg\\:grid-cols-3 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
          .preview-mobile .lg\\:grid-cols-4 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
          .preview-mobile .xl\\:grid-cols-4 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
          .preview-mobile .\\32 xl\\:grid-cols-4 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
          .preview-mobile .md\\:grid-cols-3 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
          .preview-mobile .sm\\:text-xl { font-size: 1rem !important; line-height: 1.5rem !important; }
          .preview-mobile .lg\\:text-2xl { font-size: 1.125rem !important; line-height: 1.75rem !important; }
          .preview-mobile .xl\\:text-3xl { font-size: 1.25rem !important; line-height: 1.75rem !important; }
          .preview-mobile .sm\\:text-3xl { font-size: 1.5rem !important; line-height: 2rem !important; }
          .preview-mobile .md\\:text-4xl { font-size: 1.875rem !important; line-height: 2.25rem !important; }
          .preview-mobile .lg\\:text-5xl { font-size: 2.25rem !important; line-height: 2.5rem !important; }
          .preview-mobile .sm\\:p-6 { padding: 1rem !important; }
          .preview-mobile .lg\\:p-8 { padding: 1rem !important; }
          .preview-mobile .xl\\:p-12 { padding: 1rem !important; }
          .preview-mobile .sm\\:gap-6 { gap: 1rem !important; }
          .preview-mobile .lg\\:gap-8 { gap: 1rem !important; }
          .preview-mobile .sm\\:gap-8 { gap: 1.5rem !important; }
          .preview-mobile .lg\\:gap-10 { gap: 1.5rem !important; }
          .preview-mobile .sm\\:mb-3 { margin-bottom: 0.5rem !important; }
          .preview-mobile .sm\\:mb-6 { margin-bottom: 1rem !important; }
          .preview-mobile .sm\\:mb-8 { margin-bottom: 1.5rem !important; }
          .preview-mobile .sm\\:mb-12 { margin-bottom: 2rem !important; }
          .preview-mobile .sm\\:space-y-6 > :not([hidden]) ~ :not([hidden]) { margin-top: 1rem !important; }
          .preview-mobile .sm\\:space-y-4 > :not([hidden]) ~ :not([hidden]) { margin-top: 0.75rem !important; }

          /* Tablet preview - force tablet responsive state */
          .preview-tablet .sm\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
          .preview-tablet .lg\\:grid-cols-3 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
          .preview-tablet .lg\\:grid-cols-4 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
          .preview-tablet .xl\\:grid-cols-4 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
          .preview-tablet .\\32 xl\\:grid-cols-4 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
          .preview-tablet .md\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
          .preview-tablet .sm\\:text-xl { font-size: 1.25rem !important; line-height: 1.75rem !important; }
          .preview-tablet .lg\\:text-2xl { font-size: 1.5rem !important; line-height: 2rem !important; }
          .preview-tablet .xl\\:text-3xl { font-size: 1.875rem !important; line-height: 2.25rem !important; }
          .preview-tablet .sm\\:text-3xl { font-size: 1.875rem !important; line-height: 2.25rem !important; }
          .preview-tablet .md\\:text-4xl { font-size: 2.25rem !important; line-height: 2.5rem !important; }
          .preview-tablet .lg\\:text-5xl { font-size: 3rem !important; line-height: 1 !important; }
          .preview-tablet .sm\\:p-6 { padding: 1.5rem !important; }
          .preview-tablet .lg\\:p-8 { padding: 1.5rem !important; }
          .preview-tablet .xl\\:p-12 { padding: 1.5rem !important; }
          .preview-tablet .sm\\:gap-6 { gap: 1.5rem !important; }
          .preview-tablet .lg\\:gap-8 { gap: 1.5rem !important; }
          .preview-tablet .sm\\:gap-8 { gap: 2rem !important; }
          .preview-tablet .lg\\:gap-10 { gap: 2rem !important; }
          .preview-tablet .sm\\:mb-3 { margin-bottom: 0.75rem !important; }
          .preview-tablet .sm\\:mb-6 { margin-bottom: 1.5rem !important; }
          .preview-tablet .sm\\:mb-8 { margin-bottom: 2rem !important; }
          .preview-tablet .sm\\:mb-12 { margin-bottom: 3rem !important; }
          .preview-tablet .sm\\:space-y-6 > :not([hidden]) ~ :not([hidden]) { margin-top: 1.5rem !important; }
          .preview-tablet .sm\\:space-y-4 > :not([hidden]) ~ :not([hidden]) { margin-top: 1rem !important; }
        `
      }} />

      <div className="flex flex-col h-screen bg-muted">
        <header className="flex justify-center items-center p-2 bg-background border-b gap-2">
          <Button variant={view === 'mobile' ? 'secondary' : 'ghost'} size="icon" onClick={() => setView('mobile')}><Smartphone /></Button>
          <Button variant={view === 'tablet' ? 'secondary' : 'ghost'} size="icon" onClick={() => setView('tablet')}><Tablet /></Button>
          <Button variant={view === 'desktop' ? 'secondary' : 'ghost'} size="icon" onClick={() => setView('desktop')}><Monitor /></Button>
        </header>
      <main className="flex-1 p-4 flex justify-center items-center overflow-hidden">
        <div className={cn("bg-background transition-all duration-300 ease-in-out", viewClasses[view])}>
          <div className={cn(
            "w-full h-full overflow-y-auto",
            // Apply responsive simulation classes
            view === 'mobile' && "preview-mobile",
            view === 'tablet' && "preview-tablet",
            view === 'desktop' && "preview-desktop"
          )}>
            {/* 3. Render the dynamically selected theme */}
            {renderTheme()}
          </div>
        </div>
      </main>
      </div>
    </>
  );
}