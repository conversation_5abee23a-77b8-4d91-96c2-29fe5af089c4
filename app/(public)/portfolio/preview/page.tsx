"use client";
import { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Smartphone, Tablet, Monitor } from "lucide-react";
import { cn } from "@/lib/utils";
import { PortfolioData } from "@/lib/types";

// --- NEW: Import all possible theme components ---
import { ProfolifyTheme } from "@/components/portfolio-themes/ProfolifyTheme";
import { ModernTheme } from "@/components/portfolio-themes/ModernTheme";

export default function PortfolioPreviewPage() {
  const [data, setData] = useState<PortfolioData | null>(null);
  const [view, setView] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');

  useEffect(() => {
    // 1. Get the preview data from localStorage (no change here)
    const previewData = localStorage.getItem('portfolio-preview');
    if (previewData) {
      setData(JSON.parse(previewData));
    }
  }, []);

  // Show a loader while data is being loaded from localStorage
  if (!data) {
    return <div className="flex h-screen items-center justify-center bg-muted">Loading Preview...</div>;
  }

  const viewClasses = {
    desktop: 'w-full h-full',
    tablet: 'w-full max-w-2xl h-[90%] rounded-lg shadow-2xl',
    mobile: 'w-full max-w-sm h-[90%] rounded-lg shadow-2xl',
  };

  const renderTheme = () => {
    // 2. --- FIX: Use a switch statement to render the correct theme ---
    // This logic checks the templateId from the data we got from localStorage.
    switch (data.templateId) {
      case 'modern-theme-v1':
        return <ModernTheme isEditing={false} serverData={data} />;

      case 'profolify-theme-v1':
      default:
        return <ProfolifyTheme isEditing={false} serverData={data} />;
    }
  };

  return (
    <div className="flex flex-col h-screen bg-muted">
      <header className="flex justify-center items-center p-2 bg-background border-b gap-2">
        <Button variant={view === 'mobile' ? 'secondary' : 'ghost'} size="icon" onClick={() => setView('mobile')}><Smartphone /></Button>
        <Button variant={view === 'tablet' ? 'secondary' : 'ghost'} size="icon" onClick={() => setView('tablet')}><Tablet /></Button>
        <Button variant={view === 'desktop' ? 'secondary' : 'ghost'} size="icon" onClick={() => setView('desktop')}><Monitor /></Button>
      </header>
      <main className="flex-1 p-4 flex justify-center items-center overflow-hidden">
        <div className={cn("bg-background transition-all duration-300 ease-in-out", viewClasses[view])}>
          <div className="w-full h-full overflow-y-auto">
            {/* 3. Render the dynamically selected theme */}
            {renderTheme()}
          </div>
        </div>
      </main>
    </div>
  );
}