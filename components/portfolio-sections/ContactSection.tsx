"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../portfolio-themes/types";
import { EditableText } from "@/components/ui/EditableText";
import { Gith<PERSON>, Linkedin, Twitter } from "lucide-react";
import { PortfolioData } from "@/lib/types";

export function ContactSection({ isEditing, serverData }: SectionProps) {
    const context = isEditing ? useEditor() : null;
    const data = isEditing ? context!.state.formData : serverData!;
    const dispatch = isEditing ? context!.dispatch : null;

    const handleUpdate = (field: keyof PortfolioData, value: string) => {
        if (dispatch) dispatch({ type: 'UPDATE_FIELD', payload: { field: field as any, value } });
    };

    const handleSocialUpdate = (social: keyof typeof data.socials, value: string) => {
        if (dispatch) {
            const newSocials = { ...data.socials, [social]: value };
            dispatch({ type: 'UPDATE_FIELD', payload: { field: 'socials', value: newSocials } });
        }
    };

    return (
        <section id="contact" className="py-20 lg:py-24 bg-muted/40">
            <div className="container mx-auto px-4 text-center">
                <h2 className="text-3xl font-bold mb-4">Get In Touch</h2>
                <p className="text-muted-foreground mb-8">Feel free to reach out via email or find me on social media.</p>

                <div className="mb-8">
                    <EditableText
                        isEditing={isEditing}
                        tagName="p"
                        className="text-lg text-primary font-semibold"
                        initialValue={data.contactEmail || ""}
                        onSave={(val) => handleUpdate('contactEmail', val)}
                    />
                </div>

                <div className="flex justify-center gap-6">
                    {isEditing ? (
                        <>
                            <EditableText isEditing={true} tagName="p" initialValue={data.socials?.github || ""} onSave={v => handleSocialUpdate('github', v)} className="w-1/3 text-sm" />
                            <EditableText isEditing={true} tagName="p" initialValue={data.socials?.linkedin || ""} onSave={v => handleSocialUpdate('linkedin', v)} className="w-1/3 text-sm" />
                            <EditableText isEditing={true} tagName="p" initialValue={data.socials?.twitter || ""} onSave={v => handleSocialUpdate('twitter', v)} className="w-1/3 text-sm" />
                        </>
                    ) : (
                        <>
                            <a href={data.socials?.github} target="_blank" rel="noopener noreferrer" aria-label="Github"><Github className="h-6 w-6 text-muted-foreground hover:text-primary transition-colors" /></a>
                            <a href={data.socials?.linkedin} target="_blank" rel="noopener noreferrer" aria-label="LinkedIn"><Linkedin className="h-6 w-6 text-muted-foreground hover:text-primary transition-colors" /></a>
                            <a href={data.socials?.twitter} target="_blank" rel="noopener noreferrer" aria-label="Twitter"><Twitter className="h-6 w-6 text-muted-foreground hover:text-primary transition-colors" /></a>
                        </>
                    )}
                </div>
            </div>
        </section>
    );
}