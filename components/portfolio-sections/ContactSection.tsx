"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../portfolio-themes/types";
import { EditableText } from "@/components/ui/EditableText";
import { Gith<PERSON>, Linkedin, Twitter } from "lucide-react";
import { PortfolioData } from "@/lib/types";

export function ContactSection({ isEditing, serverData }: SectionProps) {
    const context = isEditing ? useEditor() : null;
    const data = isEditing ? context!.state.formData : serverData!;
    const dispatch = isEditing ? context!.dispatch : null;

    const handleUpdate = (field: keyof PortfolioData, value: string) => {
        if (dispatch) dispatch({ type: 'UPDATE_FIELD', payload: { field: field as any, value } });
    };

    const handleSocialUpdate = (social: keyof typeof data.socials, value: string) => {
        if (dispatch) {
            const newSocials = { ...data.socials, [social]: value };
            dispatch({ type: 'UPDATE_FIELD', payload: { field: 'socials', value: newSocials } });
        }
    };

    return (
        <section id="contact" className="relative py-20 lg:py-32 bg-gradient-to-b from-muted/20 to-background overflow-hidden">
            {/* Background decoration */}
            <div className="absolute inset-0 pointer-events-none">
                <div className="absolute top-20 left-20 w-40 h-40 bg-primary/5 rounded-full blur-2xl"></div>
                <div className="absolute bottom-20 right-20 w-32 h-32 bg-primary/5 rounded-full blur-2xl"></div>
            </div>

            <div className="container mx-auto px-4 relative z-10">
                <div className="max-w-4xl mx-auto text-center">
                    {/* Section Header */}
                    <div className="mb-16">
                        <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-6">
                            <div className="w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center">
                                <div className="w-4 h-4 bg-primary rounded-full"></div>
                            </div>
                        </div>
                        <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
                            Get In Touch
                        </h2>
                        <p className="text-sm sm:text-base lg:text-lg xl:text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed px-4">
                            Ready to collaborate? Let's create something amazing together. Feel free to reach out via email or connect with me on social media.
                        </p>
                        <div className="w-24 h-1 bg-gradient-to-r from-primary to-primary/60 mx-auto rounded-full mt-6"></div>
                    </div>

                    {/* Contact Card */}
                    <div className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-2xl p-4 sm:p-6 lg:p-8 xl:p-12 shadow-xl mb-8 sm:mb-12">
                        <div className="mb-6 sm:mb-8">
                            <h3 className="text-lg sm:text-xl font-semibold mb-3 sm:mb-4 text-muted-foreground">Email Me</h3>
                            <EditableText
                                isEditing={isEditing}
                                tagName="a"
                                className="text-lg sm:text-xl lg:text-2xl xl:text-3xl text-primary font-bold hover:text-primary/80 transition-colors duration-300 break-all"
                                initialValue={data.contactEmail || "<EMAIL>"}
                                onSave={(val) => handleUpdate('contactEmail', val)}
                            />
                        </div>

                        {/* Social Links */}
                        <div className="space-y-4 sm:space-y-6">
                            <h3 className="text-lg sm:text-xl font-semibold text-muted-foreground">Connect With Me</h3>

                            {isEditing ? (
                                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                                    <div className="space-y-2">
                                        <label className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                                            <Github className="h-4 w-4" />
                                            GitHub URL
                                        </label>
                                        <EditableText
                                            isEditing={true}
                                            tagName="p"
                                            initialValue={data.socials?.github || "https://github.com/username"}
                                            onSave={v => handleSocialUpdate('github', v)}
                                            className="text-xs sm:text-sm bg-muted/50 p-2 sm:p-3 rounded-lg border"
                                        />
                                    </div>
                                    <div className="space-y-2">
                                        <label className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                                            <Linkedin className="h-4 w-4" />
                                            LinkedIn URL
                                        </label>
                                        <EditableText
                                            isEditing={true}
                                            tagName="p"
                                            initialValue={data.socials?.linkedin || "https://linkedin.com/in/username"}
                                            onSave={v => handleSocialUpdate('linkedin', v)}
                                            className="text-xs sm:text-sm bg-muted/50 p-2 sm:p-3 rounded-lg border"
                                        />
                                    </div>
                                    <div className="space-y-2">
                                        <label className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                                            <Twitter className="h-4 w-4" />
                                            Twitter URL
                                        </label>
                                        <EditableText
                                            isEditing={true}
                                            tagName="p"
                                            initialValue={data.socials?.twitter || "https://twitter.com/username"}
                                            onSave={v => handleSocialUpdate('twitter', v)}
                                            className="text-xs sm:text-sm bg-muted/50 p-2 sm:p-3 rounded-lg border"
                                        />
                                    </div>
                                </div>
                            ) : (
                                <div className="flex flex-wrap justify-center gap-4 sm:gap-6 lg:gap-8">
                                    {data.socials?.github && (
                                        <a
                                            href={data.socials.github}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            aria-label="Github"
                                            className="group flex flex-col items-center gap-2 sm:gap-3 p-3 sm:p-4 rounded-xl hover:bg-muted/50 transition-all duration-300"
                                        >
                                            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primary/10 rounded-full flex items-center justify-center group-hover:bg-primary/20 transition-colors duration-300">
                                                <Github className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
                                            </div>
                                            <span className="text-xs sm:text-sm font-medium text-muted-foreground group-hover:text-foreground transition-colors duration-300">GitHub</span>
                                        </a>
                                    )}
                                    {data.socials?.linkedin && (
                                        <a
                                            href={data.socials.linkedin}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            aria-label="LinkedIn"
                                            className="group flex flex-col items-center gap-2 sm:gap-3 p-3 sm:p-4 rounded-xl hover:bg-muted/50 transition-all duration-300"
                                        >
                                            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primary/10 rounded-full flex items-center justify-center group-hover:bg-primary/20 transition-colors duration-300">
                                                <Linkedin className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
                                            </div>
                                            <span className="text-xs sm:text-sm font-medium text-muted-foreground group-hover:text-foreground transition-colors duration-300">LinkedIn</span>
                                        </a>
                                    )}
                                    {data.socials?.twitter && (
                                        <a
                                            href={data.socials.twitter}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            aria-label="Twitter"
                                            className="group flex flex-col items-center gap-2 sm:gap-3 p-3 sm:p-4 rounded-xl hover:bg-muted/50 transition-all duration-300"
                                        >
                                            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primary/10 rounded-full flex items-center justify-center group-hover:bg-primary/20 transition-colors duration-300">
                                                <Twitter className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
                                            </div>
                                            <span className="text-xs sm:text-sm font-medium text-muted-foreground group-hover:text-foreground transition-colors duration-300">Twitter</span>
                                        </a>
                                    )}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}