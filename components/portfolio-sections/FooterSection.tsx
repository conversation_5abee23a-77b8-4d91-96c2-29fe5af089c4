"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../portfolio-themes/types";

export function FooterSection({ isEditing, serverData }: SectionProps) {
  const data = isEditing ? useEditor().state.formData : serverData!;
  const currentYear = new Date().getFullYear();

  return (
    <footer className="py-6 border-t bg-background">
      <div className="container mx-auto px-4 text-center text-sm text-muted-foreground">
        <p>© {currentYear} {data.userName}. All Rights Reserved.</p>
      </div>
    </footer>
  );
}