"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../portfolio-themes/types";

export function FooterSection({ isEditing, serverData }: SectionProps) {
  const data = isEditing ? useEditor().state.formData : serverData!;
  const currentYear = new Date().getFullYear();

  return (
    <footer className="relative py-12 border-t border-border/50 bg-gradient-to-b from-background to-muted/20">
      {/* Background decoration */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute -top-20 left-1/2 transform -translate-x-1/2 w-40 h-40 bg-primary/5 rounded-full blur-2xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center space-y-6">
          {/* Brand */}
          <div className="flex items-center justify-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary/60 rounded-lg flex items-center justify-center">
              <div className="w-4 h-4 bg-white rounded-sm"></div>
            </div>
            <span className="font-bold text-lg tracking-tight">
              {data.userName || "Portfolio"}
            </span>
          </div>

          {/* Divider */}
          <div className="w-24 h-px bg-gradient-to-r from-transparent via-border to-transparent mx-auto"></div>

          {/* Copyright */}
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">
              © {currentYear} {data.userName}. All Rights Reserved.
            </p>
            <p className="text-xs text-muted-foreground/60">
              Built with passion and attention to detail
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}