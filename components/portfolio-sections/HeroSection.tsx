"use client";
import Image from "next/image";
import { EditableText } from "@/components/ui/EditableText";
import { Button } from "@/components/ui/button";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../portfolio-themes/types";
import { Upload, Download, FileText, Loader2 } from "lucide-react";
import { PortfolioData } from "@/lib/types";
import { PortfolioImage } from "../ui/PortfolioImage";
export function HeroSection({ isEditing, serverData, onImageUpload }: SectionProps) {
    const context = isEditing ? useEditor() : null;
    const data = isEditing ? context!.state.formData : serverData!;
    const dispatch = isEditing ? context!.dispatch : null;
    const isUploadingProfile = isEditing && context?.state.isUploading?.type === 'profile';
    const isUploadingResume = isEditing && context?.state.isUploading?.type === 'resume';

    const handleUpdate = (field: keyof PortfolioData, value: string) => {
        if (dispatch) dispatch({ type: 'UPDATE_FIELD', payload: { field: field as any, value } });
    };

    return (
        <section className="flex flex-col items-center justify-center text-center py-24 px-4 bg-background">
            <div className="relative w-40 h-40 mx-auto mb-6 group">
                {/* <Image
                    width={160} height={160} priority
                    src={data.profileImageUrl || "/Image-Upload.jpg"} alt={data.userName}
                    className="w-full h-full rounded-full object-cover border-4 border-borderPrimary shadow-lg"
                /> */}
                {/* --- THIS IS THE FIX --- */}
                {/* Add a key prop that is tied directly to the image URL. */}
                {/* When data.profileImageUrl changes, the key changes, and React re-mounts the component. */}
                <PortfolioImage
                    key={data.profileImageUrl || 'placeholder'}
                    width={160} height={160} priority
                    src={data.profileImageUrl || 'https://placehold.co/160x160/e2e8f0/64748b?text=Me'}
                    alt={data.userName}
                    className="w-full h-full rounded-full object-cover border-4 border-white shadow-lg"
                />
                {isEditing && (
                    <label htmlFor="profile-upload" className="absolute inset-0 bg-black/60 rounded-full flex items-center justify-center text-white">
                        {isUploadingProfile ? <Loader2 className="animate-spin" /> : <><Upload className="mr-2 h-5 w-5" /> Change</>}
                        <input id="profile-upload" type="file" className="hidden" accept="image/*" onChange={(e) => e.target.files && onImageUpload!({ file: e.target.files[0], type: 'profile' })} disabled={isUploadingProfile} />
                    </label>
                )}
            </div>
            <EditableText isEditing={isEditing} tagName="h1" className="text-3xl max-w-md md:text-4xl font-bold tracking-tight" initialValue={data.userName} onSave={(val) => handleUpdate('userName', val)} />
            <EditableText isEditing={isEditing} tagName="p" className="text-base max-w-md md:text-lg mt-4 tracking-widest text-muted-foreground" initialValue={data.profession} onSave={(val) => handleUpdate('profession', val)} />


            <div className="mt-8 flex flex-col sm:flex-row justify-center gap-4">
                {isEditing && (
                    <Button asChild variant="outline">
                        <label className="flex items-center">
                            {isUploadingResume ? <Loader2 className="animate-spin mr-2" /> : <FileText className="mr-2 h-4 w-4" />} Upload Resume
                            <input id="resume-upload" type="file" className="hidden" accept=".pdf" onChange={(e) => e.target.files && onImageUpload!({ file: e.target.files[0], type: 'resume' })} />
                        </label>
                    </Button>
                )}
                {data.resumeUrl && <Button asChild><a href={data.resumeUrl} target="_blank" rel="noopener noreferrer"><Download className="mr-2 h-4 w-4" /> Download Resume</a></Button>}
            </div>
        </section>
    );
}