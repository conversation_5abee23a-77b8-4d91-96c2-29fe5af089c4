"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import { EditableText } from "@/components/ui/EditableText";
import { PortfolioData } from "@/lib/types";

export function AboutSection({ isEditing, serverData }: SectionProps) {
  const context = isEditing ? useEditor() : null;
  const data = isEditing ? context!.state.formData : serverData!;
  const dispatch = isEditing ? context!.dispatch : null;

  const handleUpdate = (field: keyof PortfolioData, value: string) => {
    if (dispatch) dispatch({ type: 'UPDATE_FIELD', payload: { field: field as any, value } });
  };

  return (
    <section id="about" className="py-20 lg:py-24 bg-muted/40">
      <div className="container mx-auto px-4 text-center max-w-3xl">
        <h2 className="text-3xl font-bold mb-6">About Me</h2>
        <EditableText 
          isEditing={isEditing} 
          tagName="p" 
          className="text-lg text-muted-foreground leading-relaxed" 
          initialValue={data.about || ""} 
          onSave={(val) => handleUpdate('about', val)} 
        />
      </div>
    </section>
  );
}