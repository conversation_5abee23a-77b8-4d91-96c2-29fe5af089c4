// "use client";
// import { useEditor } from "@/contexts/EditorContext";
// import { SectionProps } from "../portfolio-themes/types";
// import { Sheet, Sheet<PERSON>ontent, SheetTrigger, SheetClose } from "@/components/ui/sheet"; // --- 1. Import SheetClose ---
// import { Button } from "@/components/ui/button";
// import { Menu } from "lucide-react";

// const navLinks = [
//   { href: '#about', label: 'About' },
//   { href: '#projects', label: 'Projects' },
//   { href: '#contact', label: 'Contact' },
// ];

// export function NavbarSection({ isEditing, serverData }: SectionProps) {
//   const data = isEditing ? useEditor().state.formData : serverData!;

//   // This component is for DESKTOP view, no changes needed here.
//   const DesktopNavContent = () => (
//     <>
//       {navLinks.map(link => (
//         <a key={link.href} href={link.href} className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary">
//           {link.label}
//         </a>
//       ))}
//     </>
//   );

//   // --- NEW: A separate component for MOBILE view to apply the fix ---
//   const MobileNavContent = () => (
//     <>
//       {navLinks.map(link => (
//         // --- 2. Wrap each link with SheetClose and asChild ---
//         <SheetClose key={link.href} asChild>
//           <a href={link.href} className="text-lg font-medium transition-colors hover:text-primary">
//             {link.label}
//           </a>
//         </SheetClose>
//       ))}
//     </>
//   );

//   return (
//     <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
//       <div className="container h-14 flex items-center justify-between px-4 sm:px-6">
//         <a href="#" className="font-bold tracking-tight">{data.userName || "Portfolio"}</a>

//         <nav className="hidden md:flex gap-6 items-center">
//           <DesktopNavContent />
//         </nav>

//         <div className="md:hidden">
//           <Sheet>
//             <SheetTrigger asChild>
//               <Button variant="ghost" size="icon"><Menu className="h-5 w-5" /></Button>
//             </SheetTrigger>
//             <SheetContent side="right">
//               <nav className="flex flex-col gap-6 mt-8">
//                 {/* --- 3. Use the new MobileNavContent component --- */}
//                 <MobileNavContent />
//               </nav>
//             </SheetContent>
//           </Sheet>
//         </div>
//       </div>
//     </header>
//   );
// }

"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import { Sheet, SheetContent, SheetTrigger, SheetClose } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Menu } from "lucide-react";

const navLinks = [
  { href: '#about', label: 'About' },
  { href: '#projects', label: 'Projects' },
  { href: '#contact', label: 'Contact' },
];

export function NavbarSection({ isEditing, serverData }: SectionProps) {
  const data = isEditing ? useEditor().state.formData : serverData!;

  // A single, reusable component for the navigation links
  const NavContent = ({ isMobile = false }: { isMobile?: boolean }) => (
    <>
      {navLinks.map(link => (
        isMobile ? (
          // For the live mobile app, use SheetClose to auto-close the menu
          <SheetClose key={link.href} asChild>
            <a href={link.href} className="block rounded-md px-3 py-2 text-base font-medium text-muted-foreground hover:bg-muted hover:text-primary">
              {link.label}
            </a>
          </SheetClose>
        ) : (
          // For desktop and static export, use a simple link
          <a key={link.href} href={link.href} className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary">
            {link.label}
          </a>
        )
      ))}
    </>
  );

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container h-14 flex items-center justify-between px-4 sm:px-6">
        <a href="#" className="font-bold tracking-tight">{data.userName || "Portfolio"}</a>

        {/* Desktop Nav - Unchanged */}
        <nav className="hidden md:flex gap-6 items-center">
          <NavContent />
        </nav>

        {/* --- THIS IS THE FIX --- */}
        <div className="md:hidden">
          {/* This button works for both the live app and the static export */}
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" data-menu-button>
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right">
              {/* This nav is ONLY for the live app's interactive sheet */}
              <nav className="flex flex-col gap-2 pt-8">
                <NavContent isMobile={true} />
              </nav>
            </SheetContent>
          </Sheet>
        </div>
      </div>

      {/* This panel is ONLY for the static export. It is ignored by the live app. */}
      {/* It sits outside the SheetContent, so our script can control it. */}
      <div data-menu-panel className="hidden md:hidden bg-background border-b">
        <nav className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
          {navLinks.map(link => (
            <a key={link.href} href={link.href} data-menu-link className="block rounded-md px-3 py-2 text-base font-medium text-muted-foreground hover:bg-muted hover:text-primary">
              {link.label}
            </a>
          ))}
        </nav>
      </div>
    </header>
  );
}