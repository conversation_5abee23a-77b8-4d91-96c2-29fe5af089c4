"use client";
import { EditableText } from "@/components/ui/EditableText";
import { Button } from "@/components/ui/button";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import { Upload, Loader2, Trash, ExternalLink } from "lucide-react";
import { PortfolioImage } from "../../ui/PortfolioImage";

export function ProjectsSection({ isEditing, serverData, onImageUpload }: SectionProps) {
  const context = isEditing ? useEditor() : null;
  const data = isEditing ? context!.state.formData : serverData!;
  const dispatch = isEditing ? context!.dispatch : null;

  const handleUpdate = (index: number, field: string, value: string) => {
    if (dispatch) dispatch({ type: 'UPDATE_PROJECT', payload: { index, field: field as any, value } });
  };

  const CardContent = ({ project, index }: { project: any, index: number }) => {
    const isUploading = isEditing && context!.state.isUploading?.id === project.id;
    return (
      <>
        <div className="aspect-video bg-muted relative group/image overflow-hidden">
          {/* <Image src={project.imageUrl || 'https://placehold.co/600x400/e2e8f0/64748b?text=Project'} alt={project.title} fill className="object-cover transition-transform duration-300 group-hover/image:scale-105" /> */}
          <PortfolioImage
            key={project.imageUrl || project.id} // Use the image URL or a unique project ID as the key
            src={project.imageUrl || 'https://placehold.co/600x400/e2e8f0/64748b?text=Project'}
            alt={project.title}
            fill
            className="object-cover transition-transform duration-300 group-hover/image:scale-105"
          />
          {isEditing && (
            <label htmlFor={`project-image-${project.id}`} className="absolute inset-0 bg-black/60 flex items-center justify-center text-white opacity-0 group-hover/image:opacity-100 cursor-pointer transition-opacity">
              {isUploading ? <Loader2 className="animate-spin" /> : <Upload />}
              <input id={`project-image-${project.id}`} type="file" className="hidden" accept="image/*" onChange={(e) => e.target.files && onImageUpload!({ file: e.target.files[0], type: 'project', id: project.id })} disabled={isUploading} />
            </label>
          )}
        </div>
        <div className="p-6 flex flex-col flex-grow">
          <EditableText isEditing={isEditing} tagName="h3" className="text-xl font-bold mb-2" initialValue={project.title} onSave={(v) => handleUpdate(index, 'title', v)} />
          <EditableText isEditing={isEditing} tagName="p" className="text-muted-foreground mb-4 flex-grow" initialValue={project.description} onSave={(v) => handleUpdate(index, 'description', v)} />
          {isEditing ? (
            <div className="mt-2">
              <label className="text-xs font-bold text-muted-foreground uppercase">Project URL</label>
              <EditableText isEditing={isEditing} tagName="p" className="text-sm text-primary" initialValue={project.url || "https://example.com"} onSave={(v) => handleUpdate(index, 'url', v)} />
            </div>
          ) : (
            project.url && (
              <Button asChild variant="link" className="p-0 h-auto justify-start w-fit">
                <a href={project.url} target="_blank" rel="noopener noreferrer">View Project <ExternalLink className="ml-2 h-4 w-4" /></a>
              </Button>
            )
          )}
        </div>
      </>
    );
  };

  return (
    <section id="projects" className="py-20 lg:py-24 bg-background">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-center mb-12">Featured Projects</h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {(data.projects || []).map((project, index) => (
            <div key={project.id} className="bg-card rounded-lg shadow-md overflow-hidden group/card relative flex flex-col transition-all hover:shadow-xl hover:-translate-y-1">
              {isEditing && (
                <Button variant="destructive" size="icon" className="absolute top-2 right-2 z-10 h-8 w-8 opacity-0 group-hover/card:opacity-100" onClick={() => dispatch!({ type: 'DELETE_PROJECT', payload: { id: project.id } })}>
                  <Trash className="h-4 w-4" />
                </Button>
              )}
              <CardContent project={project} index={index} />
            </div>
          ))}
        </div>
        {isEditing && <div className="text-center mt-12"><Button onClick={() => dispatch!({ type: 'ADD_PROJECT' })}>Add Project</Button></div>}
      </div>
    </section>
  );
}