"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import { EditableText } from "@/components/ui/EditableText";
import { PortfolioData } from "@/lib/types";

export function ModernAbout({ isEditing, serverData }: SectionProps) {
    const context = isEditing ? useEditor() : null;
    const data = isEditing ? context!.state.formData : serverData!;
    const dispatch = isEditing ? context!.dispatch : null;

    const handleUpdate = (field: keyof PortfolioData, value: string) => {
        if (dispatch) dispatch({ type: 'UPDATE_FIELD', payload: { field: field as any, value } });
    };

    return (
        <section id="about" className="modern-about-bg">
            {/* Background elements */}
            <div className="absolute inset-0">
                {/* Subtle grid */}
                <div className="modern-about-grid"></div>

                {/* Floating orbs */}
                <div className="modern-about-orb-1"></div>
                <div className="modern-about-orb-2"></div>
            </div>

            <div className="modern-container relative z-10">
                <div className="max-w-6xl mx-auto">
                    {/* Section header */}
                    <div className="text-center mb-16">
                        <div className="inline-block">
                            <h2 className="modern-section-title modern-gradient-text-primary mb-4">
                                About Me
                            </h2>
                            <div className="modern-section-divider"></div>
                        </div>
                    </div>

                    {/* Content grid */}
                    <div className="grid lg:grid-cols-2 gap-12 lg:gap-20 items-center">
                        {/* Text content */}
                        <div className="space-y-8">
                            <div className="relative">
                                {/* Quote mark decoration */}
                                <div className="absolute -top-4 -left-4 text-6xl text-white font-serif">&quot;</div>
                                <EditableText
                                    isEditing={isEditing}
                                    tagName="div"
                                    className="text-lg lg:text-xl text-gray-300 leading-relaxed relative z-10"
                                    initialValue={data.about || "I'm a passionate professional dedicated to creating exceptional experiences. My journey has been shaped by curiosity, creativity, and a commitment to excellence. I believe in the power of innovation to solve complex problems and create meaningful impact."}
                                    onSave={(val) => handleUpdate('about', val)}
                                />
                            </div>
                        </div>

                        {/* Stats/Skills section */}
                        <div className="space-y-8">
                            <div className="grid grid-cols-2 gap-6">
                                <div className="modern-stat-card">
                                    <div className="modern-stat-number modern-stat-number-purple">
                                        5+
                                    </div>
                                    <div className="modern-stat-label">
                                        Years Experience
                                    </div>
                                </div>

                                <div className="modern-stat-card">
                                    <div className="modern-stat-number modern-stat-number-cyan">
                                        50+
                                    </div>
                                    <div className="modern-stat-label">
                                        Projects Completed
                                    </div>
                                </div>

                                <div className="modern-stat-card">
                                    <div className="modern-stat-number modern-stat-number-green">
                                        100%
                                    </div>
                                    <div className="modern-stat-label">
                                        Client Satisfaction
                                    </div>
                                </div>

                                <div className="modern-stat-card">
                                    <div className="modern-stat-number modern-stat-number-orange">
                                        24/7
                                    </div>
                                    <div className="modern-stat-label">
                                        Dedication
                                    </div>
                                </div>
                            </div>

                            {/* Skills or values */}
                            <div className="space-y-4">
                                <h3 className="text-xl font-semibold text-gray-200 mb-4">Core Values</h3>
                                <div className="space-y-3">
                                    {['Innovation', 'Quality', 'Collaboration', 'Growth'].map((value) => (
                                        <div key={value} className="flex items-center gap-3">
                                            <div className="modern-value-dot"></div>
                                            <span className="text-gray-300">{value}</span>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}