"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import { EditableText } from "@/components/ui/EditableText";
import { PortfolioData } from "@/lib/types";

export function ModernAbout({ isEditing, serverData }: SectionProps) {
    const context = isEditing ? useEditor() : null;
    const data = isEditing ? context!.state.formData : serverData!;
    const dispatch = isEditing ? context!.dispatch : null;

    const handleUpdate = (field: keyof PortfolioData, value: string) => {
        if (dispatch) dispatch({ type: 'UPDATE_FIELD', payload: { field: field as any, value } });
    };

    return (
        <section id="about" className="py-20 lg:py-24 bg-black text-white">
            <div className="container mx-auto px-4">
                <div className="grid md:grid-cols-3 gap-8 items-center">
                    <div className="md:col-span-1">
                        <h2 className="text-3xl font-bold">About Me</h2>
                    </div>
                    <div className="md:col-span-2">
                        <EditableText
                            isEditing={isEditing}
                            tagName="p"
                            className="text-lg text-neutral-300 leading-relaxed"
                            initialValue={data.about || ""}
                            onSave={(val) => handleUpdate('about', val)}
                        />
                    </div>
                </div>
            </div>
        </section>
    );
}