"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import { EditableText } from "@/components/ui/EditableText";
import { Github, Linkedin, Twitter, ArrowRight } from "lucide-react";
import { PortfolioData } from "@/lib/types";

export function ModernContact({ isEditing, serverData }: SectionProps) {
    const context = isEditing ? useEditor() : null;
    const data = isEditing ? context!.state.formData : serverData!;
    const dispatch = isEditing ? context!.dispatch : null;

    const handleUpdate = (field: keyof PortfolioData, value: string) => {
        if (dispatch) dispatch({ type: 'UPDATE_FIELD', payload: { field: field as any, value } });
    };

    return (
        <section id="contact" className="py-20 lg:py-24 bg-black text-white">
            <div className="container mx-auto px-4 text-center">
                <h2 className="text-3xl md:text-4xl font-bold mb-4">Let's Build Something Together</h2>
                <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">I'm currently available for freelance work and open to discussing new projects. Feel free to reach out.</p>

                <a href={`mailto:${data.contactEmail}`} className="inline-flex items-center text-lg text-primary font-semibold mb-12 group">
                    <EditableText
                        isEditing={isEditing}
                        tagName="span"
                        initialValue={data.contactEmail || ""}
                        onSave={(val) => handleUpdate('contactEmail', val)}
                    />
                    <ArrowRight className="ml-2 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
                </a>

                <div className="flex justify-center gap-8">
                    <a href={data.socials?.github} target="_blank" rel="noopener noreferrer" aria-label="Github"><Github className="h-7 w-7 text-neutral-400 hover:text-white transition-colors" /></a>
                    <a href={data.socials?.linkedin} target="_blank" rel="noopener noreferrer" aria-label="LinkedIn"><Linkedin className="h-7 w-7 text-neutral-400 hover:text-white transition-colors" /></a>
                    <a href={data.socials?.twitter} target="_blank" rel="noopener noreferrer" aria-label="Twitter"><Twitter className="h-7 w-7 text-neutral-400 hover:text-white transition-colors" /></a>
                </div>
            </div>
        </section>
    );
}