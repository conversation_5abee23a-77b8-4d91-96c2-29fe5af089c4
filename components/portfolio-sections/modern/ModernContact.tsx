"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import { EditableText } from "@/components/ui/EditableText";
import { Gith<PERSON>, Linkedin, Twitter, ArrowRight } from "lucide-react";
import { PortfolioData } from "@/lib/types";

export function ModernContact({ isEditing, serverData }: SectionProps) {
    const context = isEditing ? useEditor() : null;
    const data = isEditing ? context!.state.formData : serverData!;
    const dispatch = isEditing ? context!.dispatch : null;

    const handleUpdate = (field: keyof PortfolioData, value: string) => {
        if (dispatch) dispatch({ type: 'UPDATE_FIELD', payload: { field: field as any, value } });
    };

    const handleSocialUpdate = (platform: string, value: string) => {
        if (dispatch) {
            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'socials' as any,
                    value: { ...data.socials, [platform]: value }
                }
            });
        }
    };

    return (
        <section id="contact" className="relative py-20 lg:py-32 bg-black text-white overflow-hidden">
            {/* Background elements */}
            <div className="absolute inset-0">
                {/* Animated grid */}
                <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.01)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.01)_1px,transparent_1px)] bg-[size:120px_120px]"></div>

                {/* Central glow */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-purple-500/20 via-pink-500/20 to-cyan-500/20 rounded-full blur-3xl"></div>
            </div>

            <div className="container mx-auto px-4 text-center relative z-10">
                {/* Section header */}
                <div className="mb-16">
                    <h2 className="text-4xl sm:text-5xl lg:text-6xl font-black mb-6 bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent">
                        Let's Build Something
                    </h2>
                    <h2 className="text-4xl sm:text-5xl lg:text-6xl font-black mb-8 bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent">
                        Amazing Together
                    </h2>
                    <p className="text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed">
                        Ready to turn your vision into reality? I'm currently available for exciting projects and collaborations. Let's create something extraordinary.
                    </p>
                    <div className="w-24 h-1 bg-gradient-to-r from-purple-500 to-pink-500 mx-auto rounded-full mt-8"></div>
                </div>

                {/* Contact card */}
                <div className="max-w-2xl mx-auto mb-16">
                    <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-3xl p-8 lg:p-12 hover:border-purple-500/50 transition-all duration-500 group">
                        <div className="space-y-8">
                            {/* Email section */}
                            <div className="space-y-4">
                                <h3 className="text-xl font-semibold text-gray-300 mb-4">Drop me a line</h3>
                                <a
                                    href={`mailto:${data.contactEmail}`}
                                    className="inline-flex items-center text-2xl lg:text-3xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent hover:from-purple-300 hover:to-pink-300 transition-all duration-300 group/email"
                                >
                                    <EditableText
                                        isEditing={isEditing}
                                        tagName="span"
                                        className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent"
                                        initialValue={data.contactEmail || "<EMAIL>"}
                                        onSave={(val) => handleUpdate('contactEmail', val)}
                                    />
                                    <ArrowRight className="ml-3 h-6 w-6 text-purple-400 transition-transform duration-300 group-hover/email:translate-x-2" />
                                </a>
                            </div>

                            {/* Divider */}
                            <div className="w-full h-px bg-gradient-to-r from-transparent via-gray-700 to-transparent"></div>

                            {/* Social links */}
                            <div className="space-y-6">
                                <h3 className="text-xl font-semibold text-gray-300">Connect with me</h3>

                                {isEditing ? (
                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                        <div className="space-y-3">
                                            <label className="text-sm font-medium text-gray-400 flex items-center gap-2">
                                                <Github className="h-4 w-4" />
                                                GitHub URL
                                            </label>
                                            <EditableText
                                                isEditing={true}
                                                tagName="p"
                                                initialValue={data.socials?.github || "https://github.com/username"}
                                                onSave={v => handleSocialUpdate('github', v)}
                                                className="text-sm bg-gray-800/50 p-3 rounded-lg border border-gray-700 text-gray-300"
                                            />
                                        </div>
                                        <div className="space-y-3">
                                            <label className="text-sm font-medium text-gray-400 flex items-center gap-2">
                                                <Linkedin className="h-4 w-4" />
                                                LinkedIn URL
                                            </label>
                                            <EditableText
                                                isEditing={true}
                                                tagName="p"
                                                initialValue={data.socials?.linkedin || "https://linkedin.com/in/username"}
                                                onSave={v => handleSocialUpdate('linkedin', v)}
                                                className="text-sm bg-gray-800/50 p-3 rounded-lg border border-gray-700 text-gray-300"
                                            />
                                        </div>
                                        <div className="space-y-3">
                                            <label className="text-sm font-medium text-gray-400 flex items-center gap-2">
                                                <Twitter className="h-4 w-4" />
                                                Twitter URL
                                            </label>
                                            <EditableText
                                                isEditing={true}
                                                tagName="p"
                                                initialValue={data.socials?.twitter || "https://twitter.com/username"}
                                                onSave={v => handleSocialUpdate('twitter', v)}
                                                className="text-sm bg-gray-800/50 p-3 rounded-lg border border-gray-700 text-gray-300"
                                            />
                                        </div>
                                    </div>
                                ) : (
                                    <div className="flex justify-center gap-8">
                                        {data.socials?.github && (
                                            <a
                                                href={data.socials.github}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                aria-label="Github"
                                                className="group/social flex flex-col items-center gap-3 p-4 rounded-xl hover:bg-gray-800/50 transition-all duration-300"
                                            >
                                                <div className="w-14 h-14 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full flex items-center justify-center group-hover/social:from-purple-500/30 group-hover/social:to-pink-500/30 transition-all duration-300">
                                                    <Github className="h-7 w-7 text-gray-400 group-hover/social:text-white transition-colors duration-300" />
                                                </div>
                                                <span className="text-sm font-medium text-gray-400 group-hover/social:text-white transition-colors duration-300">GitHub</span>
                                            </a>
                                        )}
                                        {data.socials?.linkedin && (
                                            <a
                                                href={data.socials.linkedin}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                aria-label="LinkedIn"
                                                className="group/social flex flex-col items-center gap-3 p-4 rounded-xl hover:bg-gray-800/50 transition-all duration-300"
                                            >
                                                <div className="w-14 h-14 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-full flex items-center justify-center group-hover/social:from-cyan-500/30 group-hover/social:to-blue-500/30 transition-all duration-300">
                                                    <Linkedin className="h-7 w-7 text-gray-400 group-hover/social:text-white transition-colors duration-300" />
                                                </div>
                                                <span className="text-sm font-medium text-gray-400 group-hover/social:text-white transition-colors duration-300">LinkedIn</span>
                                            </a>
                                        )}
                                        {data.socials?.twitter && (
                                            <a
                                                href={data.socials.twitter}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                aria-label="Twitter"
                                                className="group/social flex flex-col items-center gap-3 p-4 rounded-xl hover:bg-gray-800/50 transition-all duration-300"
                                            >
                                                <div className="w-14 h-14 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-full flex items-center justify-center group-hover/social:from-green-500/30 group-hover/social:to-emerald-500/30 transition-all duration-300">
                                                    <Twitter className="h-7 w-7 text-gray-400 group-hover/social:text-white transition-colors duration-300" />
                                                </div>
                                                <span className="text-sm font-medium text-gray-400 group-hover/social:text-white transition-colors duration-300">Twitter</span>
                                            </a>
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Call to action */}
                <div className="text-center">
                    <p className="text-lg text-gray-400 mb-4">
                        Ready to start your project?
                    </p>
                    <div className="inline-flex items-center gap-2 text-purple-400">
                        <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                        <span className="text-sm font-medium">Available for new opportunities</span>
                        <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse delay-1000"></div>
                    </div>
                </div>
            </div>
        </section>
    );
}