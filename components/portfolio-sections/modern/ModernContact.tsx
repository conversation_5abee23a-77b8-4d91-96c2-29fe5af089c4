"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import { EditableText } from "@/components/ui/EditableText";
import { Gith<PERSON>, Linkedin, Twitter, ArrowRight } from "lucide-react";
import { PortfolioData } from "@/lib/types";

export function ModernContact({ isEditing, serverData }: SectionProps) {
    const context = isEditing ? useEditor() : null;
    const data = isEditing ? context!.state.formData : serverData!;
    const dispatch = isEditing ? context!.dispatch : null;

    const handleUpdate = (field: keyof PortfolioData, value: string) => {
        if (dispatch) dispatch({ type: 'UPDATE_FIELD', payload: { field: field as any, value } });
    };

    const handleSocialUpdate = (platform: string, value: string) => {
        if (dispatch) {
            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'socials' as any,
                    value: { ...data.socials, [platform]: value }
                }
            });
        }
    };

    return (
        <section id="contact" className="modern-contact-bg">
            {/* Background elements */}
            <div className="absolute inset-0">
                {/* Animated grid */}
                <div className="modern-contact-grid"></div>

                {/* Central glow */}
                <div className="modern-contact-glow"></div>
            </div>

            <div className="modern-container text-center relative z-10">
                {/* Section header */}
                <div className="mb-16">
                    <h2 className="modern-section-title modern-gradient-text-primary mb-6">
                        Let&#39;s Build Something
                    </h2>
                    <h2 className="modern-section-title modern-gradient-text-secondary mb-8">
                        Amazing Together
                    </h2>
                    <p className="text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed">
                        Ready to turn your vision into reality? I'm currently available for exciting projects and collaborations. Let's create something extraordinary.
                    </p>
                    <div className="modern-section-divider mt-8"></div>
                </div>

                {/* Contact card */}
                <div className="max-w-2xl mx-auto mb-16">
                    <div className="modern-contact-card">
                        <div className="space-y-8">
                            {/* Email section */}
                            <div className="space-y-4">
                                <h3 className="text-xl font-semibold text-gray-300 mb-4">Drop me a line</h3>
                                <a
                                    href={`mailto:${data.contactEmail}`}
                                    className="modern-contact-email modern-gradient-text-secondary"
                                >
                                    <EditableText
                                        isEditing={isEditing}
                                        tagName="span"
                                        className="modern-gradient-text-secondary"
                                        initialValue={data.contactEmail || "<EMAIL>"}
                                        onSave={(val) => handleUpdate('contactEmail', val)}
                                    />
                                    <ArrowRight className="ml-2 h-4 w-4 text-purple-400 transition-transform duration-300 group-hover/email:translate-x-2" />
                                </a>
                            </div>

                            {/* Divider */}
                            <div className="modern-contact-divider"></div>

                            {/* Social links */}
                            <div className="space-y-6">
                                <h3 className="text-xl font-semibold text-gray-300">Connect with me</h3>

                                {isEditing ? (
                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
                                        <div className="space-y-3">
                                            <label className="text-sm font-medium text-gray-400 flex items-center gap-2">
                                                <Github className="h-4 w-4" />
                                                GitHub URL
                                            </label>
                                            <EditableText
                                                isEditing={true}
                                                tagName="p"
                                                initialValue={data.socials?.github || "https://github.com/username"}
                                                onSave={v => handleSocialUpdate('github', v)}
                                                className="text-sm bg-gray-800/50 p-3 rounded-lg border border-gray-700 text-gray-300"
                                            />
                                        </div>
                                        <div className="space-y-3">
                                            <label className="text-sm font-medium text-gray-400 flex items-center gap-2">
                                                <Linkedin className="h-4 w-4" />
                                                LinkedIn URL
                                            </label>
                                            <EditableText
                                                isEditing={true}
                                                tagName="p"
                                                initialValue={data.socials?.linkedin || "https://linkedin.com/in/username"}
                                                onSave={v => handleSocialUpdate('linkedin', v)}
                                                className="text-sm bg-gray-800/50 p-3 rounded-lg border border-gray-700 text-gray-300"
                                            />
                                        </div>
                                        <div className="space-y-3">
                                            <label className="text-sm font-medium text-gray-400 flex items-center gap-2">
                                                <Twitter className="h-4 w-4" />
                                                Twitter URL
                                            </label>
                                            <EditableText
                                                isEditing={true}
                                                tagName="p"
                                                initialValue={data.socials?.twitter || "https://twitter.com/username"}
                                                onSave={v => handleSocialUpdate('twitter', v)}
                                                className="text-sm bg-gray-800/50 p-3 rounded-lg border border-gray-700 text-gray-300"
                                            />
                                        </div>
                                    </div>
                                ) : (
                                    <div className="flex justify-center gap-8">
                                        {data.socials?.github && (
                                            <a
                                                href={data.socials.github}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                aria-label="Github"
                                                className="modern-social-link"
                                            >
                                                <div className="modern-social-icon modern-social-icon-github">
                                                    <Github className="h-7 w-7 text-gray-400 group-hover/social:text-white transition-colors duration-300" />
                                                </div>
                                                <span className="text-sm font-medium text-gray-400 group-hover/social:text-white transition-colors duration-300">GitHub</span>
                                            </a>
                                        )}
                                        {data.socials?.linkedin && (
                                            <a
                                                href={data.socials.linkedin}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                aria-label="LinkedIn"
                                                className="modern-social-link"
                                            >
                                                <div className="modern-social-icon modern-social-icon-linkedin">
                                                    <Linkedin className="h-7 w-7 text-gray-400 group-hover/social:text-white transition-colors duration-300" />
                                                </div>
                                                <span className="text-sm font-medium text-gray-400 group-hover/social:text-white transition-colors duration-300">LinkedIn</span>
                                            </a>
                                        )}
                                        {data.socials?.twitter && (
                                            <a
                                                href={data.socials.twitter}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                aria-label="Twitter"
                                                className="modern-social-link"
                                            >
                                                <div className="modern-social-icon modern-social-icon-twitter">
                                                    <Twitter className="h-7 w-7 text-gray-400 group-hover/social:text-white transition-colors duration-300" />
                                                </div>
                                                <span className="text-sm font-medium text-gray-400 group-hover/social:text-white transition-colors duration-300">Twitter</span>
                                            </a>
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Call to action */}
                <div className="text-center">
                    <p className="text-lg text-gray-400 mb-4">
                        Ready to start your project?
                    </p>
                    <div className="inline-flex items-center gap-2 text-purple-400">
                        <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                        <span className="text-sm font-medium">Available for new opportunities</span>
                        <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse delay-1000"></div>
                    </div>
                </div>
            </div>
        </section>
    );
}