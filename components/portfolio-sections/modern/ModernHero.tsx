"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import { EditableText } from "@/components/ui/EditableText";
import { PortfolioData } from "@/lib/types";
import Image from "next/image";
import { Upload, Loader2, Download, FileText } from "lucide-react";
import { Button } from "@/components/ui/button";
import { PortfolioImage } from "@/components/ui/PortfolioImage";

export function ModernHero({ isEditing, serverData, onImageUpload }: SectionProps) {
    const context = isEditing ? useEditor() : null;
    const data = isEditing ? context!.state.formData : serverData!;
    const dispatch = isEditing ? context!.dispatch : null;
    const isUploadingProfile = isEditing && context!.state.isUploading?.type === 'profile';
    const isUploadingResume = isEditing && context!.state.isUploading?.type === 'resume';

    const handleUpdate = (field: keyof PortfolioData, value: string) => {
        if (dispatch) dispatch({ type: 'UPDATE_FIELD', payload: { field, value: value } });
    };

    return (
        <section className="bg-black text-white py-24 px-4">
            <div className="container mx-auto flex flex-col md:flex-row items-center justify-center gap-10 md:gap-16">
                <div className="relative w-48 h-48 group shrink-0">
                    {/* <Image 
                        width={192} height={192} priority 
                        src={data.profileImageUrl || 'https://i.stack.imgur.com/34AD2.jpg'} alt={data.userName}
                        className="w-full h-full rounded-full object-cover border-4 border-gray-800 shadow-lg" 
                    /> */}

                    <PortfolioImage
                        key={data.profileImageUrl || 'placeholder'}
                        width={160} height={160} priority
                        src={data.profileImageUrl || 'https://placehold.co/160x160/e2e8f0/64748b?text=Me'}
                        alt={data.userName}
                        className="w-full h-full rounded-full object-cover border-4 border-white shadow-lg"
                    />
                    {isEditing && (
                        <label htmlFor="profile-upload-modern" className="absolute inset-0 bg-black/70 rounded-full flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer">
                            {isUploadingProfile ? <Loader2 className="animate-spin" /> : <Upload className="h-6 w-6" />}
                            <input id="profile-upload-modern" type="file" className="hidden" accept="image/*" onChange={(e) => e.target.files && onImageUpload!({ file: e.target.files[0], type: 'profile' })} disabled={isUploadingProfile} />
                        </label>
                    )}
                </div>
                <div className="text-center md:text-left">
                    <EditableText isEditing={isEditing} tagName="h1" className="text-5xl lg:text-6xl font-extrabold tracking-tighter" initialValue={data.userName} onSave={(v) => handleUpdate('userName', v)} />
                    <EditableText isEditing={isEditing} tagName="p" className="text-2xl lg:text-3xl mt-2 text-primary tracking-wide" initialValue={data.profession} onSave={(v) => handleUpdate('profession', v)} />
                    <div className="mt-8 flex justify-center md:justify-start gap-4">
                        {isEditing && (
                            <Button asChild variant="outline" className="border-gray-700 hover:bg-gray-800 hover:text-white">
                                <label className="cursor-pointer flex items-center">
                                    {isUploadingResume ? <Loader2 className="animate-spin mr-2" /> : <FileText className="mr-2 h-4 w-4" />} Upload Resume
                                    <input id="resume-upload-modern" type="file" className="hidden" accept=".pdf" onChange={(e) => e.target.files && onImageUpload!({ file: e.target.files[0], type: 'resume' })} />
                                </label>
                            </Button>
                        )}
                        {data.resumeUrl && <Button asChild><a href={data.resumeUrl} target="_blank" rel="noopener noreferrer"><Download className="mr-2 h-4 w-4" /> Download Resume</a></Button>}
                    </div>
                </div>
            </div>
        </section>
    );
};