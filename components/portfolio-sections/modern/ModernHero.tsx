"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import { EditableText } from "@/components/ui/EditableText";
import { PortfolioData } from "@/lib/types";
import { Upload, Loader2, Download, FileText } from "lucide-react";
import { Button } from "@/components/ui/button";
import { PortfolioImage } from "@/components/ui/PortfolioImage";
import { useIsExport } from "@/contexts/ExportContext";

export function ModernHero({ isEditing, serverData, onImageUpload }: SectionProps) {
    const context = isEditing ? useEditor() : null;
    const data = isEditing ? context!.state.formData : serverData!;
    const dispatch = isEditing ? context!.dispatch : null;
    const isUploadingProfile = isEditing && context!.state.isUploading?.type === 'profile';
    const isUploadingResume = isEditing && context!.state.isUploading?.type === 'resume';
    const isExport = useIsExport();

    const handleUpdate = (field: keyof PortfolioData, value: string) => {
        if (dispatch) dispatch({ type: 'UPDATE_FIELD', payload: { field: field as any, value } });
    };

    return (
        <section className="relative min-h-screen bg-black text-white overflow-hidden">
            {/* Animated background */}
            <div className="absolute inset-0">
                {/* Grid pattern */}
                <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]"></div>

                {/* Gradient orbs */}
                <div className="absolute top-20 left-20 w-72 h-72 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-3xl animate-pulse"></div>
                <div className="absolute bottom-20 right-20 w-96 h-96 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-full blur-3xl animate-pulse delay-500"></div>
            </div>

            <div className="relative z-10 container mx-auto px-4 py-20 flex flex-col lg:flex-row items-center justify-center min-h-screen gap-12 lg:gap-20">
                {/* Profile Image */}
                <div className="relative group shrink-0">
                    <div className="relative w-64 h-64 lg:w-80 lg:h-80">
                        {/* Glowing ring */}
                        <div className="absolute inset-0 bg-gradient-to-r from-purple-500 via-pink-500 to-cyan-500 rounded-full blur-md opacity-75 group-hover:opacity-100 transition-opacity duration-500"></div>
                        <div className="absolute inset-2 bg-black rounded-full"></div>

                        {/* Loading overlay - shows when uploading */}
                        {isUploadingProfile && (
                            <div className="absolute inset-4 bg-black/95 rounded-full flex items-center justify-center z-30 backdrop-blur-sm">
                                <div className="flex flex-col items-center gap-4 text-white">
                                    <div className="relative">
                                        <div className="w-16 h-16 border-4 border-purple-500/30 rounded-full"></div>
                                        <div className="absolute inset-0 w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
                                    </div>
                                    <div className="text-center">
                                        <p className={`text-lg font-semibold ${
                                            isExport
                                                ? 'text-purple-300'
                                                : 'bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent'
                                        }`}>Uploading</p>
                                        <p className="text-sm text-gray-300">Please wait...</p>
                                    </div>
                                </div>
                            </div>
                        )}

                        <PortfolioImage
                            key={`modern-profile-${data.profileImageUrl || 'placeholder'}-${isUploadingProfile ? 'uploading' : 'ready'}`}
                            width={320} height={320} priority
                            src={data.profileImageUrl || 'https://placehold.co/320x320/1f2937/9ca3af?text=Profile'}
                            alt={data.userName}
                            className={`absolute inset-4 w-[calc(100%-2rem)] h-[calc(100%-2rem)] rounded-full object-cover transition-all duration-500 group-hover:scale-105 ${isUploadingProfile ? 'opacity-30' : 'opacity-100'}`}
                        />

                        {isEditing && !isUploadingProfile && (
                            <label htmlFor="profile-upload-modern" className="absolute inset-4 bg-black/80 rounded-full flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transition-all duration-300 cursor-pointer backdrop-blur-sm z-20">
                                <div className="flex flex-col items-center gap-2">
                                    <Upload className="h-8 w-8" />
                                    <span className="text-sm font-medium">Change Photo</span>
                                </div>
                                <input
                                    id="profile-upload-modern"
                                    type="file"
                                    className="hidden"
                                    accept="image/*"
                                    onChange={(e) => e.target.files && onImageUpload!({ file: e.target.files[0], type: 'profile' })}
                                    disabled={isUploadingProfile}
                                />
                            </label>
                        )}
                    </div>
                </div>

                {/* Content */}
                <div className="text-center lg:text-left max-w-2xl">
                    {/* Name with gradient */}
                    <EditableText
                        isEditing={isEditing}
                        tagName="h1"
                        className={`text-5xl sm:text-6xl lg:text-7xl xl:text-8xl font-black tracking-tighter mb-6 leading-none ${
                            isExport
                                ? 'text-white'
                                : 'bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent'
                        }`}
                        initialValue={data.userName}
                        onSave={(v) => handleUpdate('userName', v)}
                    />

                    {/* Profession with accent */}
                    <EditableText
                        isEditing={isEditing}
                        tagName="p"
                        className={`text-2xl sm:text-3xl lg:text-4xl font-light mb-8 tracking-wide ${
                            isExport
                                ? 'text-purple-300'
                                : 'bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent'
                        }`}
                        initialValue={data.profession}
                        onSave={(v) => handleUpdate('profession', v)}
                    />

                    {/* Action buttons */}
                    <div className="flex flex-col sm:flex-row justify-center lg:justify-start gap-4 mb-12">
                        {isEditing && (
                            <Button asChild variant="outline" size="lg" className="border-gray-600 bg-gray-900/50 hover:bg-gray-800 hover:border-gray-500 text-white backdrop-blur-sm transition-all duration-300 min-w-[180px]">
                                <label className="cursor-pointer flex items-center">
                                    {isUploadingResume ? (
                                        <Loader2 className="animate-spin mr-2 h-5 w-5" />
                                    ) : (
                                        <FileText className="mr-2 h-5 w-5" />
                                    )}
                                    Upload Resume
                                    <input id="resume-upload-modern" type="file" className="hidden" accept=".pdf" onChange={(e) => e.target.files && onImageUpload!({ file: e.target.files[0], type: 'resume' })} />
                                </label>
                            </Button>
                        )}
                        {data.resumeUrl && (
                            <Button asChild size="lg" className={`text-white shadow-lg hover:shadow-xl transition-all duration-300 min-w-[180px] ${
                                isExport
                                    ? 'bg-purple-600 hover:bg-purple-700'
                                    : 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700'
                            }`}>
                                <a href={data.resumeUrl} target="_blank" rel="noopener noreferrer">
                                    <Download className="mr-2 h-5 w-5" />
                                    Download Resume
                                </a>
                            </Button>
                        )}
                    </div>

                    {/* Scroll indicator */}
                    <div className="flex justify-center lg:justify-start">
                        <div className="animate-bounce">
                            <div className="w-6 h-10 border-2 border-gray-600 rounded-full flex justify-center">
                                <div className="w-1 h-3 bg-gray-400 rounded-full mt-2 animate-pulse"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};