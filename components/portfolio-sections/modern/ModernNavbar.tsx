"use client";
import { SectionProps } from "../../portfolio-themes/types";
import { Menu, X } from "lucide-react";
import { useState } from "react";
import { useEditor } from "@/contexts/EditorContext";
const navLinks = [
    { href: '#about', label: 'About' },
    { href: '#projects', label: 'Work' },
    { href: '#contact', label: 'Contact' },
];

export function ModernNavbar({ isEditing, serverData }: SectionProps) {
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

    // Always call useEditor hook, but handle the error gracefully
    let editorData = null;
    try {
        const context = useEditor();
        editorData = context.state.formData;
    } catch {
        // Editor context not available (public view), use serverData
        editorData = null;
    }

    // Use editor data if available and in editing mode, otherwise use serverData
    const data = (isEditing && editorData) ? editorData : (serverData || { userName: 'Portfolio' });

    const toggleMobileMenu = () => {
        setIsMobileMenuOpen(!isMobileMenuOpen);
    };

    const closeMobileMenu = () => {
        setIsMobileMenuOpen(false);
    };

    return (
        <header className="modern-navbar">
            <div className="modern-navbar-container">
                <a href="#" className="modern-navbar-brand">{data.userName || "Portfolio"}</a>

                {/* Desktop Navigation */}
                <nav className="modern-navbar-nav">
                    {navLinks.map(link => (
                        <a key={link.href} href={link.href} className="modern-navbar-link">
                            {link.label}
                        </a>
                    ))}
                </nav>

                {/* Mobile Menu Button */}
                <div className="md:hidden">
                    <button
                        type="button"
                        onClick={toggleMobileMenu}
                        className="modern-navbar-mobile-button"
                        aria-label="Toggle navigation menu"
                        {...(isMobileMenuOpen ? { 'aria-expanded': 'true' } : { 'aria-expanded': 'false' })}
                    >
                        {isMobileMenuOpen ? (
                            <X className="h-5 w-5" />
                        ) : (
                            <Menu className="h-5 w-5" />
                        )}
                    </button>
                </div>
            </div>

            {/* Mobile Menu Panel */}
            <div className={`modern-navbar-mobile-panel ${isMobileMenuOpen ? 'modern-navbar-mobile-panel-open' : ''}`}>
                <nav className="modern-navbar-mobile-nav">
                    {navLinks.map(link => (
                        <a
                            key={link.href}
                            href={link.href}
                            className="modern-navbar-mobile-link"
                            onClick={closeMobileMenu}
                        >
                            {link.label}
                        </a>
                    ))}
                </nav>
            </div>

            {/* Mobile Menu Overlay */}
            {isMobileMenuOpen && (
                <div
                    className="modern-navbar-mobile-overlay"
                    onClick={closeMobileMenu}
                />
            )}
        </header>
    );
};
