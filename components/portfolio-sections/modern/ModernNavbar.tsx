"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import { Menu, X } from "lucide-react";
import { useState } from "react";
const navLinks = [
    { href: '#about', label: 'About' },
    { href: '#projects', label: 'Work' },
    { href: '#contact', label: 'Contact' },
];

export function ModernNavbar({ isEditing, serverData }: SectionProps) {
    const context = useEditor();
    const data = isEditing ? context.state.formData : serverData!;
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

    const toggleMobileMenu = () => {
        setIsMobileMenuOpen(!isMobileMenuOpen);
    };

    const closeMobileMenu = () => {
        setIsMobileMenuOpen(false);
    };

    return (
        <header className="modern-navbar">
            <div className="modern-navbar-container">
                <a href="#" className="modern-navbar-brand">{data.userName || "Portfolio"}</a>

                {/* Desktop Navigation */}
                <nav className="modern-navbar-nav">
                    {navLinks.map(link => (
                        <a key={link.href} href={link.href} className="modern-navbar-link">
                            {link.label}
                        </a>
                    ))}
                </nav>

                {/* Mobile Menu Button */}
                <div className="md:hidden">
                    <button
                        type="button"
                        onClick={toggleMobileMenu}
                        className="modern-navbar-mobile-button"
                        aria-label="Toggle navigation menu"
                        aria-expanded={isMobileMenuOpen ? "true" : "false"}
                    >
                        {isMobileMenuOpen ? (
                            <X className="h-5 w-5" />
                        ) : (
                            <Menu className="h-5 w-5" />
                        )}
                    </button>
                </div>
            </div>

            {/* Mobile Menu Panel */}
            <div className={`modern-navbar-mobile-panel ${isMobileMenuOpen ? 'modern-navbar-mobile-panel-open' : ''}`}>
                <nav className="modern-navbar-mobile-nav">
                    {navLinks.map(link => (
                        <a
                            key={link.href}
                            href={link.href}
                            className="modern-navbar-mobile-link"
                            onClick={closeMobileMenu}
                        >
                            {link.label}
                        </a>
                    ))}
                </nav>
            </div>

            {/* Mobile Menu Overlay */}
            {isMobileMenuOpen && (
                <div
                    className="modern-navbar-mobile-overlay"
                    onClick={closeMobileMenu}
                />
            )}
        </header>
    );
};
