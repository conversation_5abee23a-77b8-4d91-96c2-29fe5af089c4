// This component already had a good structure. We'll add the smooth-scroll links.
"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Menu } from "lucide-react";
const navLinks = [
    { href: '#about', label: 'About' },
    { href: '#projects', label: 'Work' },
    { href: '#contact', label: 'Contact' },
];
export function ModernNavbar({ isEditing, serverData }: SectionProps) {
    const data = isEditing ? useEditor().state.formData : serverData!;
    const NavContent = () => (
        <>
            {navLinks.map(link => (
                <a key={link.href} href={link.href} className="text-sm font-medium transition-colors hover:text-primary">
                    {link.label}
                </a>
            ))}
        </>
    );

    return (
        <header className="sticky top-0 z-50 w-full border-b border-gray-800 bg-gray-950/95 backdrop-blur supports-[backdrop-filter]:bg-gray-950/60 text-white">
            {/* <div className="container h-14 flex items-center justify-between">
                <a href="#" className="font-bold tracking-widest uppercase">{data.userName || "Portfolio"}</a>

                <nav className="hidden md:flex gap-6 items-center">
                    <NavContent />
                </nav>

                <div className="md:hidden">
                    <Sheet>
                        <SheetTrigger asChild>
                            <Button variant="ghost" size="icon" className="hover:bg-gray-800 hover:text-white"><Menu className="h-5 w-5" /></Button>
                        </SheetTrigger>
                        <SheetContent side="right" className="bg-gray-950 text-white border-l-gray-800">
                            <nav className="flex flex-col gap-6 text-lg font-medium mt-8">
                                <NavContent />
                            </nav>
                        </SheetContent>
                    </Sheet>
                </div>
            </div> */}
            <div className="container h-14 flex items-center justify-between px-4 sm:px-6">
                <a href="#" className="font-bold tracking-tight">{data.userName || "Portfolio"}</a>

                <nav className="hidden md:flex gap-6 items-center">
                    <NavContent />
                </nav>

                {/* Mobile Menu */}
                <div className="md:hidden">
                    <Sheet>
                        <SheetTrigger asChild>
                            <button data-menu-button className="inline-flex items-center justify-center p-2 rounded-md hover:bg-gray-800 hover:text-white transition-colors">
                                <Menu className="h-5 w-5" />
                            </button>
                        </SheetTrigger>
                        <SheetContent side="right" className="bg-gray-950 text-white border-l-gray-800">
                            <nav className="flex flex-col gap-6 text-lg font-medium mt-8">
                                {navLinks.map(link => (
                                    <a key={link.href} href={link.href} className="text-sm font-medium transition-colors hover:text-primary">
                                        {link.label}
                                    </a>
                                ))}
                            </nav>
                        </SheetContent>
                    </Sheet>
                </div>

            </div>

            {/* Static Export Mobile Menu Panel */}
            <div data-menu-panel className="hidden md:hidden bg-gray-950/95 backdrop-blur border-b border-gray-800">
                <nav className="px-4 pt-4 pb-6 space-y-2">
                    {navLinks.map(link => (
                        <a
                            key={link.href}
                            href={link.href}
                            data-menu-link
                            className="block rounded-lg px-4 py-3 text-base font-medium text-gray-300 hover:bg-gray-800 hover:text-white transition-all duration-300"
                        >
                            {link.label}
                        </a>
                    ))}
                </nav>
            </div>
        </header>
    );
};
