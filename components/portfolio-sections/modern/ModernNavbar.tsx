// This component already had a good structure. We'll add the smooth-scroll links.
"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import { Sheet, Sheet<PERSON>ontent, Sheet<PERSON>rigger, SheetTitle } from "@/components/ui/sheet";
import { Menu } from "lucide-react";
const navLinks = [
    { href: '#about', label: 'About' },
    { href: '#projects', label: 'Work' },
    { href: '#contact', label: 'Contact' },
];
export function ModernNavbar({ isEditing, serverData }: SectionProps) {
    const data = isEditing ? useEditor().state.formData : serverData!;
    const NavContent = () => (
        <>
            {navLinks.map(link => (
                <a key={link.href} href={link.href} className="modern-navbar-link">
                    {link.label}
                </a>
            ))}
        </>
    );

    return (
        <header className="modern-navbar">

            <div className="modern-navbar-container">
                <a href="#" className="modern-navbar-brand">{data.userName || "Portfolio"}</a>

                <nav className="modern-navbar-nav">
                    <NavContent />
                </nav>

                {/* Mobile Menu */}
                <div className="md:hidden">
                    <Sheet>
                        <SheetTrigger asChild>
                            <button
                                data-menu-button
                                className="inline-flex items-center justify-center p-2 rounded-md hover:bg-gray-800 hover:text-white transition-colors"
                                aria-label="Open navigation menu"
                                title="Open navigation menu"
                            >
                                <Menu className="h-5 w-5" />
                            </button>
                        </SheetTrigger>
                        <SheetContent side="right" className="bg-gray-950 text-white border-l-gray-800">
                            <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
                            <nav className="flex flex-col gap-6 text-lg font-medium mt-8 items-center text-center">
                                {navLinks.map(link => (
                                    <a key={link.href} href={link.href} className="text-lg font-medium transition-colors hover:text-purple-400 text-white">
                                        {link.label}
                                    </a>
                                ))}
                            </nav>
                        </SheetContent>
                    </Sheet>
                </div>

            </div>

            {/* Static Export Mobile Menu Panel */}
            <div data-menu-panel className="modern-navbar-mobile-panel">
                <nav className="modern-navbar-mobile-nav">
                    {navLinks.map(link => (
                        <a
                            key={link.href}
                            href={link.href}
                            data-menu-link
                            className="modern-navbar-mobile-link"
                        >
                            {link.label}
                        </a>
                    ))}
                </nav>
            </div>
        </header>
    );
};
