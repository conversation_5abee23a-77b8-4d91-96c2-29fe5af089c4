"use client";
import { EditableText } from "@/components/ui/EditableText";
import { Button } from "@/components/ui/button";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import { Upload, Trash, ExternalLink } from "lucide-react";
import { PortfolioImage } from "@/components/ui/PortfolioImage";

export function ModernProjects({ isEditing, serverData, onImageUpload }: SectionProps) {
    const context = isEditing ? useEditor() : null;
    const data = isEditing ? context!.state.formData : serverData!;
    const dispatch = isEditing ? context!.dispatch : null;

    const handleUpdate = (index: number, field: string, value: string) => {
        if (dispatch) dispatch({ type: 'UPDATE_PROJECT', payload: { index, field: field as any, value } });
    };

    return (
        <section id="projects" className="modern-projects-bg">
            {/* Background elements */}
            <div className="absolute inset-0">
                {/* Animated grid */}
                <div className="modern-projects-grid"></div>

                {/* Gradient orbs */}
                <div className="modern-projects-orb-1"></div>
                <div className="modern-projects-orb-2"></div>
            </div>

            <div className="modern-container relative z-10">
                {/* Section header */}
                <div className="text-center mb-20">
                    <h2 className="modern-section-title modern-gradient-text-primary">
                        Featured Work
                    </h2>
                    <p className="text-xl text-gray-400 max-w-2xl mx-auto leading-relaxed">
                        Showcasing projects that demonstrate creativity, technical expertise, and innovative solutions
                    </p>
                    <div className="modern-section-divider"></div>
                </div>

                {/* Projects grid */}
                <div className="space-y-24 lg:space-y-32">
                    {(data.projects || []).map((project, index) => {
                        const isUploading = isEditing && context!.state.isUploading?.type === 'project' && context!.state.isUploading?.id === project.id;
                        const isEven = index % 2 === 0;

                        return (
                            <div key={project.id} className="group">
                                <div className={`grid lg:grid-cols-2 gap-12 lg:gap-16 items-center ${!isEven ? 'lg:grid-flow-col-dense' : ''}`}>
                                    {/* Project Image */}
                                    <div className={`relative ${!isEven ? 'lg:col-start-2' : ''}`}>
                                        <div className="relative aspect-[4/3] rounded-2xl overflow-hidden bg-gray-900 group-hover:shadow-2xl group-hover:shadow-purple-500/20 transition-all duration-500">
                                            {/* Glowing border effect */}
                                            <div className="modern-project-glow group-hover:opacity-100"></div>

                                            {/* Loading overlay - shows when uploading */}
                                            {isUploading && (
                                                <div className="absolute inset-0 bg-black/95 flex items-center justify-center z-40 backdrop-blur-sm rounded-2xl">
                                                    <div className="flex flex-col items-center gap-4 text-white">
                                                        <div className="relative">
                                                            <div className="w-16 h-16 border-4 border-purple-500/30 rounded-full"></div>
                                                            <div className="absolute inset-0 w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
                                                        </div>
                                                        <div className="text-center">
                                                            <p className="modern-project-upload-text">Uploading Image</p>
                                                            <p className="text-sm text-gray-300">Please wait...</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            )}

                                            <div className="relative z-10 w-full h-full rounded-2xl overflow-hidden">
                                                <PortfolioImage
                                                    key={`modern-project-${project.id}-${project.imageUrl || 'placeholder'}-${isUploading ? 'uploading' : 'ready'}`}
                                                    src={project.imageUrl || 'https://placehold.co/800x600/1f2937/9ca3af?text=Project'}
                                                    alt={project.title}
                                                    fill
                                                    className={`object-cover transition-all duration-700 group-hover:scale-110 ${isUploading ? 'opacity-30' : 'opacity-100'}`}
                                                />

                                                {/* Overlay gradient */}
                                                <div className="modern-project-overlay group-hover:opacity-100"></div>
                                            </div>

                                            {isEditing && !isUploading && (
                                                <label htmlFor={`project-image-${project.id}`} className="absolute inset-0 bg-black/80 flex flex-col items-center justify-center text-white opacity-0 group-hover:opacity-100 cursor-pointer transition-all duration-300 backdrop-blur-sm rounded-2xl z-20">
                                                    <div className="flex flex-col items-center gap-3">
                                                        <Upload className="h-8 w-8" />
                                                        <span className="text-sm font-medium">Change Image</span>
                                                    </div>
                                                    <input
                                                        key={`modern-project-input-${project.id}`}
                                                        id={`project-image-${project.id}`}
                                                        type="file"
                                                        className="hidden"
                                                        accept="image/*"
                                                        onChange={(e) => {
                                                            if (e.target.files) {
                                                                onImageUpload!({ file: e.target.files[0], type: 'project', id: project.id });
                                                                // Reset the input value to allow re-uploading the same file
                                                                e.target.value = '';
                                                            }
                                                        }}
                                                        disabled={isUploading}
                                                    />
                                                </label>
                                            )}
                                        </div>
                                    </div>

                                    {/* Project Content */}
                                    <div className={`space-y-6 ${!isEven ? 'lg:col-start-1' : ''}`}>
                                        <div className="space-y-4">
                                            <div className="flex items-center gap-3">
                                                <div className="modern-project-number"></div>
                                                <span className="text-sm font-medium text-gray-400 uppercase tracking-wider">
                                                    Project {String(index + 1).padStart(2, '0')}
                                                </span>
                                            </div>

                                            <EditableText
                                                isEditing={isEditing}
                                                tagName="h3"
                                                className="modern-project-title modern-gradient-text-primary"
                                                initialValue={project.title}
                                                onSave={(v) => handleUpdate(index, 'title', v)}
                                            />
                                        </div>

                                        <EditableText
                                            isEditing={isEditing}
                                            tagName="div"
                                            className="text-lg text-gray-300 leading-relaxed"
                                            initialValue={project.description}
                                            onSave={(v) => handleUpdate(index, 'description', v)}
                                        />

                                        <div className="flex flex-col sm:flex-row gap-4 pt-4">
                                            {isEditing ? (
                                                <div className="space-y-2">
                                                    <label className="text-xs font-semibold text-gray-400 uppercase tracking-wider">Project URL</label>
                                                    <EditableText
                                                        isEditing={isEditing}
                                                        tagName="p"
                                                        className="text-sm text-purple-400 bg-gray-900/50 px-3 py-2 rounded-lg border border-gray-800"
                                                        initialValue={project.url || "https://example.com"}
                                                        onSave={(v) => handleUpdate(index, 'url', v)}
                                                    />
                                                </div>
                                            ) : project.url && (
                                                <Button asChild variant="outline" size="lg" className="border-gray-700 bg-gray-900/50 hover:bg-gray-800 hover:border-purple-500 text-white backdrop-blur-sm transition-all duration-300">
                                                    <a href={project.url} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2">
                                                        View Project
                                                        <ExternalLink className="h-4 w-4" />
                                                    </a>
                                                </Button>
                                            )}

                                            {isEditing && (
                                                <Button
                                                    variant="destructive"
                                                    size="lg"
                                                    className="bg-red-900/50 hover:bg-red-800 border border-red-800 hover:border-red-700 backdrop-blur-sm transition-all duration-300"
                                                    onClick={() => dispatch!({ type: 'DELETE_PROJECT', payload: { id: project.id } })}
                                                >
                                                    <Trash className="h-4 w-4 mr-2" />
                                                    Delete Project
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>

                {/* Add project button */}
                {isEditing && (
                    <div className="text-center mt-20">
                        <Button
                            onClick={() => dispatch!({ type: 'ADD_PROJECT' })}
                            size="lg"
                            className="modern-button-primary shadow-lg hover:shadow-xl transition-all duration-300"
                        >
                            Add New Project
                        </Button>
                    </div>
                )}
            </div>
        </section>
    );
}