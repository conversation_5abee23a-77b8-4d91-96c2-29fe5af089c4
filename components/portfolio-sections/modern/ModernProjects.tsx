"use client";
import { EditableText } from "@/components/ui/EditableText";
import { Button } from "@/components/ui/button";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import Link from 'next/link';
import { Upload, Loader2, Trash, ExternalLink } from "lucide-react";
import Image from "next/image";
import { PortfolioImage } from "@/components/ui/PortfolioImage";

export function ModernProjects({ isEditing, serverData, onImageUpload }: SectionProps) {
    const context = isEditing ? useEditor() : null;
    const data = isEditing ? context!.state.formData : serverData!;
    const dispatch = isEditing ? context!.dispatch : null;

    const handleUpdate = (index: number, field: string, value: string) => {
        if (dispatch) dispatch({ type: 'UPDATE_PROJECT', payload: { index, field: field as any, value } });
    };

    return (
        <section id="projects" className="py-20 bg-gray-950">
            <div className="container mx-auto px-4">
                <h2 className="text-4xl font-bold text-center mb-12 text-white">Featured Work</h2>
                <div className="flex flex-col gap-12">
                    {(data.projects || []).map((project, index) => {
                        const isUploading = isEditing && context!.state.isUploading?.id === project.id;
                        const content = (
                            <div key={project.id} className="grid md:grid-cols-2 gap-8 items-center group/card">
                                <div className={`relative aspect-video rounded-lg overflow-hidden group/image ${index % 2 !== 0 ? 'md:order-2' : ''}`}>
                                    {/* <Image src={project.imageUrl || 'https://placehold.co/1280x720/171717/ffffff?text=Project'} alt={project.title} fill className="object-cover transition-transform duration-500 group-hover/card:scale-105" /> */}
                                    <PortfolioImage
                                        key={project.imageUrl || project.id} // Use the image URL or a unique project ID as the key
                                        src={project.imageUrl || 'https://placehold.co/600x400/e2e8f0/64748b?text=Project'}
                                        alt={project.title}
                                        fill
                                        className="object-cover transition-transform duration-300 group-hover/image:scale-105"
                                    />
                                    {isEditing && (
                                        <label htmlFor={`project-image-${project.id}`} className="absolute inset-0 bg-black/70 flex items-center justify-center text-white opacity-0 group-hover/image:opacity-100 cursor-pointer transition-opacity">
                                            {isUploading ? <Loader2 className="animate-spin" /> : <Upload />}
                                            <input id={`project-image-${project.id}`} type="file" className="hidden" accept="image/*" onChange={(e) => e.target.files && onImageUpload!({ file: e.target.files[0], type: 'project', id: project.id })} disabled={isUploading} />
                                        </label>
                                    )}
                                </div>
                                <div className={`text-white ${index % 2 !== 0 ? 'md:order-1' : ''}`}>
                                    <EditableText isEditing={isEditing} tagName="h3" className="text-3xl font-bold mb-3" initialValue={project.title} onSave={(v) => handleUpdate(index, 'title', v)} />
                                    <EditableText isEditing={isEditing} tagName="p" className="text-neutral-300 mb-4 leading-relaxed" initialValue={project.description} onSave={(v) => handleUpdate(index, 'description', v)} />
                                    {isEditing ? (
                                        <div className="mt-2">
                                            <label className="text-xs font-bold text-neutral-400">URL</label>
                                            <EditableText isEditing={isEditing} tagName="p" className="text-sm text-primary" initialValue={project.url || "https://example.com"} onSave={(v) => handleUpdate(index, 'url', v)} />
                                        </div>
                                    ) : project.url && (
                                        <Button asChild variant="link" className="p-0 text-primary">
                                            <a href={project.url} target="_blank" rel="noopener noreferrer">View Project <ExternalLink className="ml-2 h-4 w-4" /></a>
                                        </Button>
                                    )}
                                    {isEditing && (
                                        <Button variant="destructive" size="sm" className="mt-4" onClick={() => dispatch!({ type: 'DELETE_PROJECT', payload: { id: project.id } })}>
                                            <Trash className="h-4 w-4 mr-2" /> Delete Project
                                        </Button>
                                    )}
                                </div>
                            </div>
                        );
                        return content;
                    })}
                </div>
                {isEditing && <div className="text-center mt-16"><Button onClick={() => dispatch!({ type: 'ADD_PROJECT' })}>Add New Project</Button></div>}
            </div>
        </section>
    );
}