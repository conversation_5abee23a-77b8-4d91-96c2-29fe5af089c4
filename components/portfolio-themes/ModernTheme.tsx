"use client";
import { ProfolifyThemeProps } from "./types";
import { ModernNavbar } from "../portfolio-sections/modern/ModernNavbar";
import { ModernHero } from "../portfolio-sections/modern/ModernHero";
import { ModernAbout } from "../portfolio-sections/modern/ModernAbout";
import { ModernProjects } from "../portfolio-sections/modern/ModernProjects";
import { ModernContact } from "../portfolio-sections/modern/ModernContact";
import { ModernFooter } from "../portfolio-sections/modern/ModernFooter";

export function ModernTheme({ isEditing, serverData, onImageUpload }: ProfolifyThemeProps) {
    return (
        <div className="bg-black text-white font-sans">
            <ModernNavbar isEditing={isEditing} serverData={serverData} />
            <main>
                <ModernHero isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
                <ModernAbout isEditing={isEditing} serverData={serverData} />
                <ModernProjects isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
                <ModernContact isEditing={isEditing} serverData={serverData} />
            </main>
            <ModernFooter isEditing={isEditing} serverData={serverData} />
        </div>
    );
}