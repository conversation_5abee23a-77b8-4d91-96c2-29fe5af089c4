"use client";
import { ProfolifyThemeProps } from "./types";
import { HeroSection } from "../portfolio-sections/creative/HeroSection";
import { AboutSection } from "../portfolio-sections/creative/AboutSection";
import { ProjectsSection } from "../portfolio-sections/creative/ProjectsSection";
import { ContactSection } from "../portfolio-sections/creative/ContactSection";
import { FooterSection } from "../portfolio-sections/creative/FooterSection";
import { NavbarSection } from "../portfolio-sections/creative/NavbarSection";

export function ProfolifyTheme({ isEditing, serverData, onImageUpload }: ProfolifyThemeProps) {
  return (
    <div className="bg-background text-foreground font-sans">
      <NavbarSection isEditing={isEditing} serverData={serverData} />
      <main>
        <HeroSection isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
        <AboutSection isEditing={isEditing} serverData={serverData} />
        <ProjectsSection isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
        <ContactSection isEditing={isEditing} serverData={serverData} />
      </main>
      <FooterSection isEditing={isEditing} serverData={serverData} />
    </div>
  );
}