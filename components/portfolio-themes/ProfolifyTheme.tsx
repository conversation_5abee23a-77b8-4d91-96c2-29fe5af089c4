"use client";
import { ProfolifyThemeProps } from "./types";
import { HeroSection } from "../portfolio-sections/HeroSection";
import { AboutSection } from "../portfolio-sections/AboutSection";
import { ProjectsSection } from "../portfolio-sections/ProjectsSection";
import { ContactSection } from "../portfolio-sections/ContactSection";
import { FooterSection } from "../portfolio-sections/FooterSection";
import { NavbarSection } from "../portfolio-sections/NavbarSection";

export function ProfolifyTheme({ isEditing, serverData, onImageUpload }: ProfolifyThemeProps) {
  return (
    <div className="bg-background text-foreground font-sans">
      <NavbarSection isEditing={isEditing} serverData={serverData} />
      <main>
        <HeroSection isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
        <AboutSection isEditing={isEditing} serverData={serverData} />
        <ProjectsSection isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
        <ContactSection isEditing={isEditing} serverData={serverData} />
      </main>
      <FooterSection isEditing={isEditing} serverData={serverData} />
    </div>
  );
}