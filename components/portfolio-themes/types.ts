import { PortfolioData } from "@/lib/types";

// A type for upload mutations, makes passing it easier
export type UploadFunction = (vars: { file: File; type: string; id?: string }) => void;

export interface ProfolifyThemeProps {
    isEditing: boolean;
    serverData?: PortfolioData; // For public, server-rendered pages
    onImageUpload?: UploadFunction; // For editor page
}

// Props for each section component
export interface SectionProps {
    isEditing: boolean;
    serverData?: PortfolioData;
    onImageUpload?: UploadFunction;
}