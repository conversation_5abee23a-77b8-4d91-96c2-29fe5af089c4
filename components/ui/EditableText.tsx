"use client";
import React, { useState, useEffect } from 'react';
import ContentEditable, { ContentEditableEvent } from 'react-contenteditable';
import { cn } from '@/lib/utils';

interface EditableTextProps {
    initialValue: string;
    isEditing: boolean;
    onSave: (value: string) => void;
    className?: string;
    tagName?: 'h1' | 'h2' | 'h3' | 'p' | 'div' | 'span';
}

export function EditableText({ initialValue, isEditing, onSave, className, tagName = 'div' }: EditableTextProps) {
    // --- FIX #1: Give the component its own internal state ---
    const [text, setText] = useState(initialValue || '');

    // --- FIX #2: Sync internal state if the prop from above changes ---
    // This is important for initial data loading and external updates.
    useEffect(() => {
        setText(initialValue || '');
    }, [initialValue]);

    // Handle live changes from the user's typing
    const handleChange = (evt: ContentEditableEvent) => {
        setText(evt.target.value);
    };

    // On blur, save the final state back to the main application context
    const handleBlur = () => {
        onSave(text);
    };

    // If we are in "edit mode", render the interactive component.
    if (isEditing) {
        return (
            <ContentEditable
                html={text} // Use the local state for the value
                onChange={handleChange} // Update the local state on every keystroke
                onBlur={handleBlur} // Save the final state on blur
                tagName={tagName}
                className={cn(
                    "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-md -m-1 p-1 transition-all cursor-text",
                    className
                )}
            />
        );
    }

    // If not editing, render a simple, static HTML tag.
    // This uses the initialValue prop for display, which is efficient.
    return React.createElement(
        tagName,
        { className: className },
        initialValue
    );
}