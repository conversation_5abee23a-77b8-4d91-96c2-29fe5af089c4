import type { NextApiRequest, NextApiResponse } from 'next';
import { renderToStaticMarkup } from 'react-dom/server';
import J<PERSON><PERSON><PERSON> from 'jszip';
import { getPortfolio } from '@/lib/portfolio-api';
import { auth } from '@/lib/firebase-admin';
import { ProfolifyTheme } from '@/components/portfolio-themes/ProfolifyTheme';
import { ModernTheme } from '@/components/portfolio-themes/ModernTheme';
import React from 'react';
import { ExportProvider } from '@/contexts/ExportContext';

// Helper to get the theme component
const getThemeComponent = (templateId: string) => {
    return templateId === 'modern-theme-v1' ? ModernTheme : ProfolifyTheme;
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', 'POST');
        return res.status(405).end('Method Not Allowed');
    }

    try {
        const token = req.headers.authorization?.split('Bearer ')[1];
        if (!token) { return res.status(401).json({ error: 'Unauthorized' }); }
        const decodedToken = await auth.verifyIdToken(token);
        const userId = decodedToken.uid;

        const portfolioData = await getPortfolio(userId);
        if (!portfolioData) { return res.status(404).json({ error: 'Portfolio not found' }); }

        const ThemeComponent = getThemeComponent(portfolioData.templateId);
        const staticHtml = renderToStaticMarkup(
            React.createElement(
                ExportProvider,
                { value: true },
                React.createElement(ThemeComponent, { isEditing: false, serverData: portfolioData })
            )
        );

        // --- THIS IS THE SIMPLIFIED AND CORRECTED SCRIPT ---
        const interactivityScript = `
        <script>
            document.addEventListener('DOMContentLoaded', () => {
                const menuButton = document.querySelector('[data-menu-button]');
                const menuPanel = document.querySelector('[data-menu-panel]');
                
                if (menuButton && menuPanel) {
                    menuButton.addEventListener('click', (e) => {
                        // We stop the event to prevent the live app's Sheet from trying to open
                        e.preventDefault();
                        e.stopPropagation();
                        
                        // Toggle a simple 'hidden' class from Tailwind CSS
                        menuPanel.classList.toggle('hidden');
                    });

                    // Close menu when any link inside it is clicked
                    menuPanel.querySelectorAll('[data-menu-link]').forEach(link => {
                        link.addEventListener('click', () => {
                            menuPanel.classList.add('hidden');
                        });
                    });
                }
            });
        </script>
        `;

        const fullHtml = `<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${portfolioData.userName}'s Portfolio</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="root">${staticHtml}</div>
    ${interactivityScript}
</body>
</html>`;

        const cssContent = `
/* Custom CSS for exported portfolio */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: system-ui, -apple-system, sans-serif;
    line-height: 1.6;
}

/* Ensure proper text visibility in exports */
.text-foreground {
    color: #0f172a !important;
}

.text-muted-foreground {
    color: #64748b !important;
}

.text-primary {
    color: #3b82f6 !important;
}

.bg-primary {
    background-color: #3b82f6 !important;
}

.bg-card {
    background-color: #ffffff !important;
}

.bg-background {
    background-color: #ffffff !important;
}

.bg-muted {
    background-color: #f8fafc !important;
}

.border-border {
    border-color: #e2e8f0 !important;
}

/* Fix gradient backgrounds for export */
.bg-gradient-to-br {
    background: linear-gradient(to bottom right, var(--tw-gradient-stops)) !important;
}

.bg-gradient-to-b {
    background: linear-gradient(to bottom, var(--tw-gradient-stops)) !important;
}

.bg-gradient-to-r {
    background: linear-gradient(to right, var(--tw-gradient-stops)) !important;
}

/* Ensure buttons are visible and properly styled */
button, .btn, [role="button"] {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0.5rem 1rem !important;
    border-radius: 0.375rem !important;
    font-weight: 500 !important;
    transition: all 0.2s !important;
    text-decoration: none !important;
    border: none !important;
    cursor: pointer !important;
}

/* Specific button size classes */
.h-12 {
    height: 3rem !important;
}

.min-w-\\[160px\\] {
    min-width: 160px !important;
}

/* Fix hover states for export */
a:hover, button:hover {
    opacity: 0.8 !important;
}

/* Ensure text is visible in all contexts */
h1, h2, h3, h4, h5, h6, p, span, div, a {
    color: inherit !important;
}

/* Fix specific text color classes */
.text-4xl, .text-5xl, .text-6xl {
    font-size: inherit !important;
    font-weight: bold !important;
}

/* Ensure proper spacing */
.space-y-4 > * + * {
    margin-top: 1rem !important;
}

.gap-4 {
    gap: 1rem !important;
}

.mb-4 {
    margin-bottom: 1rem !important;
}

.mb-8 {
    margin-bottom: 2rem !important;
}

.mb-12 {
    margin-bottom: 3rem !important;
}

/* Responsive utilities */
@media (max-width: 640px) {
    .container {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }
}
`;
        const zip = new JSZip();
        zip.file("index.html", fullHtml);
        zip.file("styles.css", cssContent);
        zip.file("README.md", "Portfolio generated by Profolify.");

        const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });

        res.setHeader('Content-Type', 'application/zip');
        res.setHeader('Content-Disposition', `attachment; filename="${portfolioData.slug || 'portfolio'}.zip"`);
        return res.status(200).send(zipBuffer);

    } catch (error: any) {
        console.error("Export failed:", error);
        return res.status(500).json({ error: 'Failed to export portfolio' });
    }
}