import type { NextApiRequest, NextApiResponse } from 'next';
import { renderToStaticMarkup } from 'react-dom/server';
import J<PERSON><PERSON><PERSON> from 'jszip';
import { getPortfolio } from '@/lib/portfolio-api';
import { auth } from '@/lib/firebase-admin';
import { ProfolifyTheme } from '@/components/portfolio-themes/ProfolifyTheme';
import { ModernTheme } from '@/components/portfolio-themes/ModernTheme';
import React from 'react';
import { ExportProvider } from '@/contexts/ExportContext';

// Helper to get the theme component
const getThemeComponent = (templateId: string) => {
    return templateId === 'modern-theme-v1' ? ModernTheme : ProfolifyTheme;
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', 'POST');
        return res.status(405).end('Method Not Allowed');
    }

    try {
        const token = req.headers.authorization?.split('Bearer ')[1];
        if (!token) { return res.status(401).json({ error: 'Unauthorized' }); }
        const decodedToken = await auth.verifyIdToken(token);
        const userId = decodedToken.uid;

        const portfolioData = await getPortfolio(userId);
        if (!portfolioData) { return res.status(404).json({ error: 'Portfolio not found' }); }

        const ThemeComponent = getThemeComponent(portfolioData.templateId);
        const staticHtml = renderToStaticMarkup(
            React.createElement(
                ExportProvider,
                { value: true },
                React.createElement(ThemeComponent, { isEditing: false, serverData: portfolioData })
            )
        );

        // --- THIS IS THE SIMPLIFIED AND CORRECTED SCRIPT ---
        const interactivityScript = `
        <script>
            document.addEventListener('DOMContentLoaded', () => {
                const menuButton = document.querySelector('[data-menu-button]');
                const menuPanel = document.querySelector('[data-menu-panel]');
                
                if (menuButton && menuPanel) {
                    menuButton.addEventListener('click', (e) => {
                        // We stop the event to prevent the live app's Sheet from trying to open
                        e.preventDefault();
                        e.stopPropagation();
                        
                        // Toggle a simple 'hidden' class from Tailwind CSS
                        menuPanel.classList.toggle('hidden');
                    });

                    // Close menu when any link inside it is clicked
                    menuPanel.querySelectorAll('[data-menu-link]').forEach(link => {
                        link.addEventListener('click', () => {
                            menuPanel.classList.add('hidden');
                        });
                    });
                }
            });
        </script>
        `;

        const fullHtml = `<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${portfolioData.userName}'s Portfolio</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="root">${staticHtml}</div>
    ${interactivityScript}
</body>
</html>`;

        const cssContent = `/* Add your custom CSS rules here. */`;
        const zip = new JSZip();
        zip.file("index.html", fullHtml);
        zip.file("styles.css", cssContent);
        zip.file("README.md", "Portfolio generated by Profolify.");

        const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });

        res.setHeader('Content-Type', 'application/zip');
        res.setHeader('Content-Disposition', `attachment; filename="${portfolioData.slug || 'portfolio'}.zip"`);
        return res.status(200).send(zipBuffer);

    } catch (error: any) {
        console.error("Export failed:", error);
        return res.status(500).json({ error: 'Failed to export portfolio' });
    }
}