import type { NextApiRequest, NextApiResponse } from 'next';
import { renderToStaticMarkup } from 'react-dom/server';
import J<PERSON><PERSON><PERSON> from 'jszip';
import { getPortfolio } from '@/lib/portfolio-api';
import { auth } from '@/lib/firebase-admin';
import { ProfolifyTheme } from '@/components/portfolio-themes/ProfolifyTheme';
import { ModernTheme } from '@/components/portfolio-themes/ModernTheme';
import React from 'react';
import { ExportProvider } from '@/contexts/ExportContext';

// Helper to get the theme component
const getThemeComponent = (templateId: string) => {
    return templateId === 'modern-theme-v1' ? ModernTheme : ProfolifyTheme;
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', 'POST');
        return res.status(405).end('Method Not Allowed');
    }

    try {
        const token = req.headers.authorization?.split('Bearer ')[1];
        if (!token) { return res.status(401).json({ error: 'Unauthorized' }); }
        const decodedToken = await auth.verifyIdToken(token);
        const userId = decodedToken.uid;

        const portfolioData = await getPortfolio(userId);
        if (!portfolioData) { return res.status(404).json({ error: 'Portfolio not found' }); }

        const ThemeComponent = getThemeComponent(portfolioData.templateId);
        const staticHtml = renderToStaticMarkup(
            React.createElement(
                ExportProvider,
                { value: true },
                React.createElement(ThemeComponent, { isEditing: false, serverData: portfolioData })
            )
        );

        // Custom Mobile Menu Script for Static Export
        const interactivityScript = `
        <script>
            document.addEventListener('DOMContentLoaded', () => {
                // Custom mobile menu functionality
                const mobileButtons = document.querySelectorAll('.modern-navbar-mobile-button');
                const mobilePanels = document.querySelectorAll('.modern-navbar-mobile-panel');

                mobileButtons.forEach((button, index) => {
                    const panel = mobilePanels[index];
                    if (!panel) return;

                    let isOpen = false;

                    button.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();

                        isOpen = !isOpen;

                        if (isOpen) {
                            panel.classList.add('modern-navbar-mobile-panel-open');
                            button.setAttribute('aria-expanded', 'true');
                            // Change icon to X
                            const icon = button.querySelector('svg');
                            if (icon) {
                                icon.innerHTML = '<path d="M18 6L6 18M6 6l12 12"></path>';
                            }

                            // Create overlay
                            const overlay = document.createElement('div');
                            overlay.className = 'modern-navbar-mobile-overlay';
                            overlay.addEventListener('click', closeMenu);
                            document.body.appendChild(overlay);
                        } else {
                            closeMenu();
                        }
                    });

                    function closeMenu() {
                        isOpen = false;
                        panel.classList.remove('modern-navbar-mobile-panel-open');
                        button.setAttribute('aria-expanded', 'false');
                        // Change icon back to menu
                        const icon = button.querySelector('svg');
                        if (icon) {
                            icon.innerHTML = '<path d="M3 12h18M3 6h18M3 18h18"></path>';
                        }

                        // Remove overlay
                        const overlay = document.querySelector('.modern-navbar-mobile-overlay');
                        if (overlay) {
                            overlay.remove();
                        }
                    }

                    // Close menu when clicking on links
                    panel.querySelectorAll('.modern-navbar-mobile-link').forEach(link => {
                        link.addEventListener('click', closeMenu);
                    });
                });
            });
        </script>
        `;

        const fullHtml = `<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${portfolioData.userName}'s Portfolio</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="root">${staticHtml}</div>
    ${interactivityScript}
</body>
</html>`;

        const cssContent = `
/* Custom CSS for exported portfolio */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: system-ui, -apple-system, sans-serif;
    line-height: 1.6;
}

/* Modern Theme Custom Styles */
.modern-theme {
    background-color: #000000;
    color: #ffffff;
    font-family: system-ui, -apple-system, sans-serif;
}

/* Gradient text effects */
.modern-gradient-text-primary {
    background: linear-gradient(to right, #ffffff, #f3f4f6, #d1d5db);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 900;
}

.modern-gradient-text-secondary {
    background: linear-gradient(to right, #a855f7, #ec4899, #06b6d4);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.modern-gradient-text-accent {
    background: linear-gradient(to right, #8b5cf6, #ec4899);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 600;
}

/* Button styles */
.modern-button-primary {
    background: linear-gradient(to right, #9333ea, #db2777);
    color: #ffffff;
    padding: 0.75rem 2rem;
    border-radius: 0.5rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    min-width: 180px;
    justify-content: center;
}

.modern-button-primary:hover {
    background: linear-gradient(to right, #7c3aed, #be185d);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(147, 51, 234, 0.3);
}

/* Container styles */
.modern-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 640px) {
    .modern-container {
        padding: 0 1.5rem;
    }
}

@media (min-width: 1024px) {
    .modern-container {
        padding: 0 2rem;
    }
}

/* Hero section styles */
.modern-hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.modern-hero-title {
    font-size: 1.5rem;
    line-height: 1;
    margin-bottom: 1.5rem;
    letter-spacing: -0.025em;
    font-weight: 900;
}

@media (min-width: 640px) {
    .modern-hero-title {
        font-size: 2.25rem;
    }
}

@media (min-width: 1024px) {
    .modern-hero-title {
        font-size: 3rem;
    }
}

@media (min-width: 1280px) {
    .modern-hero-title {
        font-size: 3.75rem;
    }
}

.modern-hero-subtitle {
    font-size: 1.25rem;
    font-weight: 300;
    margin-bottom: 2rem;
    letter-spacing: 0.025em;
}

@media (min-width: 640px) {
    .modern-hero-subtitle {
        font-size: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .modern-hero-subtitle {
        font-size: 1.875rem;
    }
}

/* Hero background decorations */
.modern-hero-bg {
    position: relative;
    background-color: #000000;
    overflow: hidden;
}

.modern-hero-grid {
    position: absolute;
    inset: 0;
    background-image:
        linear-gradient(rgba(255,255,255,0.02) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255,255,255,0.02) 1px, transparent 1px);
    background-size: 50px 50px;
}

.modern-hero-orb-1 {
    position: absolute;
    top: 5rem;
    left: 5rem;
    width: 18rem;
    height: 18rem;
    background: linear-gradient(to right, rgba(168, 85, 247, 0.2), rgba(236, 72, 153, 0.2));
    border-radius: 50%;
    filter: blur(3rem);
    animation: pulse 2s infinite;
}

.modern-hero-orb-2 {
    position: absolute;
    bottom: 5rem;
    right: 5rem;
    width: 24rem;
    height: 24rem;
    background: linear-gradient(to right, rgba(59, 130, 246, 0.2), rgba(6, 182, 212, 0.2));
    border-radius: 50%;
    filter: blur(3rem);
    animation: pulse 2s infinite;
    animation-delay: 1s;
}

.modern-hero-orb-3 {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16rem;
    height: 16rem;
    background: linear-gradient(to right, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.1));
    border-radius: 50%;
    filter: blur(3rem);
    animation: pulse 2s infinite;
    animation-delay: 0.5s;
}

/* Profile image styles */
.modern-profile-container {
    position: relative;
    flex-shrink: 0;
}

.modern-profile-wrapper {
    position: relative;
    width: 16rem;
    height: 16rem;
}

@media (min-width: 1024px) {
    .modern-profile-wrapper {
        width: 20rem;
        height: 20rem;
    }
}

.modern-profile-ring {
    position: absolute;
    inset: 0;
    background: linear-gradient(to right, #a855f7, #ec4899, #06b6d4);
    border-radius: 50%;
    filter: blur(0.375rem);
    opacity: 0.75;
    transition: opacity 0.5s ease;
}

.modern-profile-container:hover .modern-profile-ring {
    opacity: 1;
}

.modern-profile-bg {
    position: absolute;
    inset: 0.5rem;
    background-color: #000000;
    border-radius: 50%;
}

/* Loading states */
.modern-loading-text {
    font-size: 1.125rem;
    font-weight: 600;
}

/* Project section styles */
.modern-projects-bg {
    position: relative;
    background-color: #000000;
    padding: 5rem 0;
    overflow: hidden;
}

@media (min-width: 1024px) {
    .modern-projects-bg {
        padding: 8rem 0;
    }
}

.modern-projects-grid {
    position: absolute;
    inset: 0;
    background-image:
        linear-gradient(rgba(255,255,255,0.02) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255,255,255,0.02) 1px, transparent 1px);
    background-size: 80px 80px;
    animation: pulse 2s infinite;
}

.modern-projects-orb-1 {
    position: absolute;
    top: 5rem;
    left: 5rem;
    width: 24rem;
    height: 24rem;
    background: linear-gradient(to right, rgba(168, 85, 247, 0.1), rgba(236, 72, 153, 0.1));
    border-radius: 50%;
    filter: blur(3rem);
}

.modern-projects-orb-2 {
    position: absolute;
    bottom: 5rem;
    right: 5rem;
    width: 20rem;
    height: 20rem;
    background: linear-gradient(to right, rgba(6, 182, 212, 0.1), rgba(59, 130, 246, 0.1));
    border-radius: 50%;
    filter: blur(3rem);
}

.modern-section-title {
    font-size: 2.25rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
}

@media (min-width: 640px) {
    .modern-section-title {
        font-size: 3rem;
    }
}

@media (min-width: 1024px) {
    .modern-section-title {
        font-size: 3.75rem;
    }
}

.modern-section-divider {
    width: 6rem;
    height: 0.25rem;
    background: linear-gradient(to right, #a855f7, #ec4899);
    margin: 1.5rem auto 0;
    border-radius: 9999px;
}

.modern-project-title {
    font-size: 1.875rem;
    font-weight: 700;
    line-height: 1.2;
}

@media (min-width: 1024px) {
    .modern-project-title {
        font-size: 2.25rem;
    }
}

.modern-project-description {
    font-size: 1.125rem;
    color: #d1d5db;
    line-height: 1.6;
}

.modern-project-number {
    width: 2rem;
    height: 1px;
    background: linear-gradient(to right, #a855f7, #ec4899);
}

.modern-project-glow {
    position: absolute;
    inset: 0;
    background: linear-gradient(to right, rgba(168, 85, 247, 0.2), rgba(236, 72, 153, 0.2), rgba(6, 182, 212, 0.2));
    border-radius: 1rem;
    filter: blur(0.375rem);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.modern-project-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent, transparent);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.modern-project-upload-text {
    font-size: 1.125rem;
    font-weight: 600;
    background: linear-gradient(to right, #a855f7, #ec4899);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

/* Navbar styles */
.modern-navbar {
    position: sticky;
    top: 0;
    z-index: 50;
    width: 100%;
    border-bottom: 1px solid #374151;
    background-color: rgba(3, 7, 18, 0.95);
    backdrop-filter: blur(8px);
    color: #ffffff;
}

.modern-navbar-container {
    height: 3.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1rem;
    max-width: 1200px;
    margin: 0 auto;
}

@media (min-width: 640px) {
    .modern-navbar-container {
        padding: 0 1.5rem;
    }
}

.modern-navbar-brand {
    font-weight: 700;
    letter-spacing: -0.025em;
    color: #ffffff;
    text-decoration: none;
}

.modern-navbar-nav {
    display: none;
    gap: 1.5rem;
    align-items: center;
}

@media (min-width: 768px) {
    .modern-navbar-nav {
        display: flex;
    }
}

.modern-navbar-link {
    font-size: 0.875rem;
    font-weight: 500;
    color: #ffffff;
    text-decoration: none;
    transition: color 0.3s ease;
}

.modern-navbar-link:hover {
    color: #a855f7;
}

/* Custom Mobile Menu */
.modern-navbar-mobile-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    border-radius: 0.375rem;
    background-color: transparent;
    border: none;
    color: #ffffff;
    transition: all 0.3s ease;
    cursor: pointer;
}

.modern-navbar-mobile-button:hover {
    background-color: rgba(55, 65, 81, 0.5);
    color: #ffffff;
}

.modern-navbar-mobile-panel {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: rgba(3, 7, 18, 0.98);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border-bottom: 1px solid #374151;
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 40;
}

.modern-navbar-mobile-panel-open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
}

@media (max-width: 767px) {
    .modern-navbar-mobile-panel {
        display: block;
    }
}

.modern-navbar-mobile-nav {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    padding: 2rem 1rem;
    text-align: center;
}

.modern-navbar-mobile-link {
    font-size: 1.125rem;
    font-weight: 500;
    color: #ffffff;
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
}

.modern-navbar-mobile-link:hover {
    color: #a855f7;
    background-color: rgba(168, 85, 247, 0.1);
}

.modern-navbar-mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 30;
}

/* About section styles */
.modern-about-bg {
    position: relative;
    background-color: #000000;
    padding: 5rem 0;
    overflow: hidden;
}

@media (min-width: 1024px) {
    .modern-about-bg {
        padding: 8rem 0;
    }
}

.modern-about-grid {
    position: absolute;
    inset: 0;
    background-image:
        linear-gradient(rgba(255,255,255,0.01) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255,255,255,0.01) 1px, transparent 1px);
    background-size: 100px 100px;
}

.modern-about-orb-1 {
    position: absolute;
    top: 10rem;
    right: 5rem;
    width: 16rem;
    height: 16rem;
    background: linear-gradient(to right, rgba(168, 85, 247, 0.1), rgba(236, 72, 153, 0.1));
    border-radius: 50%;
    filter: blur(3rem);
}

.modern-about-orb-2 {
    position: absolute;
    bottom: 5rem;
    left: 5rem;
    width: 12rem;
    height: 12rem;
    background: linear-gradient(to right, rgba(59, 130, 246, 0.1), rgba(6, 182, 212, 0.1));
    border-radius: 50%;
    filter: blur(3rem);
}

/* Contact section styles */
.modern-contact-bg {
    position: relative;
    background-color: #000000;
    padding: 5rem 0;
    overflow: hidden;
}

@media (min-width: 1024px) {
    .modern-contact-bg {
        padding: 8rem 0;
    }
}

.modern-contact-grid {
    position: absolute;
    inset: 0;
    background-image:
        linear-gradient(rgba(255,255,255,0.01) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255,255,255,0.01) 1px, transparent 1px);
    background-size: 120px 120px;
}

.modern-contact-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 24rem;
    height: 24rem;
    background: linear-gradient(to right, rgba(168, 85, 247, 0.2), rgba(236, 72, 153, 0.2), rgba(6, 182, 212, 0.2));
    border-radius: 50%;
    filter: blur(3rem);
}

.modern-contact-card {
    background-color: rgba(17, 24, 39, 0.5);
    backdrop-filter: blur(0.375rem);
    border: 1px solid #374151;
    border-radius: 1.5rem;
    padding: 2rem;
    transition: all 0.5s ease;
}

@media (min-width: 1024px) {
    .modern-contact-card {
        padding: 3rem;
    }
}

.modern-contact-card:hover {
    border-color: rgba(168, 85, 247, 0.5);
}

/* Footer styles */
.modern-footer {
    background-color: #000000;
    border-top: 1px solid #374151;
    position: relative;
    overflow: hidden;
    padding: 1rem;
}

.modern-footer-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffffff;
}

@media (min-width: 640px) {
    .modern-footer-title {
        font-size: 1.875rem;
    }
}

.modern-footer-subtitle {
    font-size: 1.125rem;
}

.modern-footer-text {
    color: #9ca3af;
    font-size: 0.875rem;
}

.modern-footer-brand {
    color: #6b7280;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    justify-content: center;
}

.modern-footer-brand-name {
    font-weight: 600;
}

/* Animation utilities */
.modern-fade-in {
    animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Ensure proper text rendering */
.modern-theme * {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
`;
        const zip = new JSZip();
        zip.file("index.html", fullHtml);
        zip.file("styles.css", cssContent);
        zip.file("README.md", "Portfolio generated by Profolify.");

        const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });

        res.setHeader('Content-Type', 'application/zip');
        res.setHeader('Content-Disposition', `attachment; filename="${portfolioData.slug || 'portfolio'}.zip"`);
        return res.status(200).send(zipBuffer);

    } catch (error: any) {
        console.error("Export failed:", error);
        return res.status(500).json({ error: 'Failed to export portfolio' });
    }
}