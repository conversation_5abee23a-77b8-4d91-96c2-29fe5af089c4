/* Modern Theme Custom Styles */

/* Base theme styles */
.modern-theme {
    background-color: #000000;
    color: #ffffff;
    font-family: system-ui, -apple-system, sans-serif;
}

/* Gradient text effects */
.modern-gradient-text-primary {
    background: linear-gradient(to right, #ffffff, #f3f4f6, #d1d5db);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 900;
}

.modern-gradient-text-secondary {
    background: linear-gradient(to right, #a855f7, #ec4899, #06b6d4);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.modern-gradient-text-secondary {
    background: linear-gradient(to right, #a855f7, #ec4899, #06b6d4);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.modern-gradient-text-accent {
    background: linear-gradient(to right, #8b5cf6, #ec4899);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 600;
}

/* Button styles */
.modern-button-primary {
    background: linear-gradient(to right, #9333ea, #db2777);
    color: #ffffff;
    padding: 0.75rem 2rem;
    border-radius: 0.5rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    min-width: 180px;
    justify-content: center;
}

.modern-button-primary:hover {
    background: linear-gradient(to right, #7c3aed, #be185d);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(147, 51, 234, 0.3);
}

/* Container styles */
.modern-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 640px) {
    .modern-container {
        padding: 0 1.5rem;
    }
}

@media (min-width: 1024px) {
    .modern-container {
        padding: 0 2rem;
    }
}

/* Hero section styles */
.modern-hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.modern-hero-title {
    font-size: 1.5rem;
    line-height: 1;
    margin-bottom: 1.5rem;
    letter-spacing: -0.025em;
    font-weight: 900;
}

@media (min-width: 640px) {
    .modern-hero-title {
        font-size: 2.25rem;
    }
}

@media (min-width: 1024px) {
    .modern-hero-title {
        font-size: 3rem;
    }
}

@media (min-width: 1280px) {
    .modern-hero-title {
        font-size: 3.75rem;
    }
}

.modern-hero-subtitle {
    font-size: 1.25rem;
    font-weight: 300;
    margin-bottom: 2rem;
    letter-spacing: 0.025em;
}

@media (min-width: 640px) {
    .modern-hero-subtitle {
        font-size: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .modern-hero-subtitle {
        font-size: 1.875rem;
    }
}

/* Hero background decorations */
.modern-hero-bg {
    position: relative;
    background-color: #000000;
    overflow: hidden;
}

.modern-hero-grid {
    position: absolute;
    inset: 0;
    background-image:
        linear-gradient(rgba(255,255,255,0.02) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255,255,255,0.02) 1px, transparent 1px);
    background-size: 50px 50px;
}

.modern-hero-orb-1 {
    position: absolute;
    top: 5rem;
    left: 5rem;
    width: 18rem;
    height: 18rem;
    background: linear-gradient(to right, rgba(168, 85, 247, 0.2), rgba(236, 72, 153, 0.2));
    border-radius: 50%;
    filter: blur(3rem);
    animation: pulse 2s infinite;
}

.modern-hero-orb-2 {
    position: absolute;
    bottom: 5rem;
    right: 5rem;
    width: 24rem;
    height: 24rem;
    background: linear-gradient(to right, rgba(59, 130, 246, 0.2), rgba(6, 182, 212, 0.2));
    border-radius: 50%;
    filter: blur(3rem);
    animation: pulse 2s infinite;
    animation-delay: 1s;
}

.modern-hero-orb-3 {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16rem;
    height: 16rem;
    background: linear-gradient(to right, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.1));
    border-radius: 50%;
    filter: blur(3rem);
    animation: pulse 2s infinite;
    animation-delay: 0.5s;
}

/* Profile image styles */
.modern-profile-container {
    position: relative;
    flex-shrink: 0;
}

.modern-profile-wrapper {
    position: relative;
    width: 16rem;
    height: 16rem;
}

@media (min-width: 1024px) {
    .modern-profile-wrapper {
        width: 20rem;
        height: 20rem;
    }
}

.modern-profile-ring {
    position: absolute;
    inset: 0;
    background: linear-gradient(to right, #a855f7, #ec4899, #06b6d4);
    border-radius: 50%;
    filter: blur(0.375rem);
    opacity: 0.75;
    transition: opacity 0.5s ease;
}

.modern-profile-container:hover .modern-profile-ring {
    opacity: 1;
}

.modern-profile-bg {
    position: absolute;
    inset: 0.5rem;
    background-color: #000000;
    border-radius: 50%;
}

/* Loading states */
.modern-loading-text {
    font-size: 1.125rem;
    font-weight: 600;
}

/* Project section styles */
.modern-projects-bg {
    position: relative;
    background-color: #000000;
    padding: 5rem 0;
    overflow: hidden;
}

@media (min-width: 1024px) {
    .modern-projects-bg {
        padding: 8rem 0;
    }
}

.modern-projects-grid {
    position: absolute;
    inset: 0;
    background-image:
        linear-gradient(rgba(255,255,255,0.02) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255,255,255,0.02) 1px, transparent 1px);
    background-size: 80px 80px;
    animation: pulse 2s infinite;
}

.modern-projects-orb-1 {
    position: absolute;
    top: 5rem;
    left: 5rem;
    width: 24rem;
    height: 24rem;
    background: linear-gradient(to right, rgba(168, 85, 247, 0.1), rgba(236, 72, 153, 0.1));
    border-radius: 50%;
    filter: blur(3rem);
}

.modern-projects-orb-2 {
    position: absolute;
    bottom: 5rem;
    right: 5rem;
    width: 20rem;
    height: 20rem;
    background: linear-gradient(to right, rgba(6, 182, 212, 0.1), rgba(59, 130, 246, 0.1));
    border-radius: 50%;
    filter: blur(3rem);
}

.modern-section-title {
    font-size: 2.25rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
}

@media (min-width: 640px) {
    .modern-section-title {
        font-size: 3rem;
    }
}

@media (min-width: 1024px) {
    .modern-section-title {
        font-size: 3.75rem;
    }
}

.modern-section-divider {
    width: 6rem;
    height: 0.25rem;
    background: linear-gradient(to right, #a855f7, #ec4899);
    margin: 1.5rem auto 0;
    border-radius: 9999px;
}

.modern-project-title {
    font-size: 1.875rem;
    font-weight: 700;
    line-height: 1.2;
}

@media (min-width: 1024px) {
    .modern-project-title {
        font-size: 2.25rem;
    }
}

.modern-project-description {
    font-size: 1.125rem;
    color: #d1d5db;
    line-height: 1.6;
}

.modern-project-number {
    width: 2rem;
    height: 1px;
    background: linear-gradient(to right, #a855f7, #ec4899);
}

.modern-project-glow {
    position: absolute;
    inset: 0;
    background: linear-gradient(to right, rgba(168, 85, 247, 0.2), rgba(236, 72, 153, 0.2), rgba(6, 182, 212, 0.2));
    border-radius: 1rem;
    filter: blur(0.375rem);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.modern-project-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent, transparent);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.modern-project-upload-text {
    font-size: 1.125rem;
    font-weight: 600;
    background: linear-gradient(to right, #a855f7, #ec4899);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

/* Navbar styles */
.modern-navbar {
    position: sticky;
    top: 0;
    z-index: 50;
    width: 100%;
    border-bottom: 1px solid #374151;
    background-color: rgba(3, 7, 18, 0.95);
    backdrop-filter: blur(8px);
    color: #ffffff;
}

.modern-navbar-container {
    height: 3.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1rem;
    max-width: 1200px;
    margin: 0 auto;
}

@media (min-width: 640px) {
    .modern-navbar-container {
        padding: 0 1.5rem;
    }
}

.modern-navbar-brand {
    font-weight: 700;
    letter-spacing: -0.025em;
    color: #ffffff;
    text-decoration: none;
}

.modern-navbar-nav {
    display: none;
    gap: 1.5rem;
    align-items: center;
}

@media (min-width: 768px) {
    .modern-navbar-nav {
        display: flex;
    }
}

.modern-navbar-link {
    font-size: 0.875rem;
    font-weight: 500;
    color: #ffffff;
    text-decoration: none;
    transition: color 0.3s ease;
}

.modern-navbar-link:hover {
    color: #a855f7;
}

.modern-navbar-mobile-panel {
    display: none;
    background-color: rgba(3, 7, 18, 0.95);
    backdrop-filter: blur(8px);
    border-bottom: 1px solid #374151;
}

@media (max-width: 767px) {
    .modern-navbar-mobile-panel {
        display: block;
    }
}

.modern-navbar-mobile-nav {
    padding: 1rem 1rem 1.5rem;
    margin-top: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.modern-navbar-mobile-link {
    display: block;
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    font-weight: 500;
    color: #d1d5db;
    text-decoration: none;
    transition: all 0.3s ease;
}

.modern-navbar-mobile-link:hover {
    background-color: #374151;
    color: #ffffff;
}

/* About section styles */
.modern-about-bg {
    position: relative;
    background-color: #000000;
    padding: 5rem 0;
    overflow: hidden;
}

@media (min-width: 1024px) {
    .modern-about-bg {
        padding: 8rem 0;
    }
}

.modern-about-grid {
    position: absolute;
    inset: 0;
    background-image:
        linear-gradient(rgba(255,255,255,0.01) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255,255,255,0.01) 1px, transparent 1px);
    background-size: 100px 100px;
}

.modern-about-orb-1 {
    position: absolute;
    top: 10rem;
    right: 5rem;
    width: 16rem;
    height: 16rem;
    background: linear-gradient(to right, rgba(168, 85, 247, 0.1), rgba(236, 72, 153, 0.1));
    border-radius: 50%;
    filter: blur(3rem);
}

.modern-about-orb-2 {
    position: absolute;
    bottom: 5rem;
    left: 5rem;
    width: 12rem;
    height: 12rem;
    background: linear-gradient(to right, rgba(59, 130, 246, 0.1), rgba(6, 182, 212, 0.1));
    border-radius: 50%;
    filter: blur(3rem);
}

.modern-stat-card {
    background-color: rgba(17, 24, 39, 0.5);
    backdrop-filter: blur(0.375rem);
    border: 1px solid #374151;
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    transition: border-color 0.3s ease;
}

.modern-stat-card:hover {
    border-color: rgba(168, 85, 247, 0.5);
}

.modern-stat-card:nth-child(2):hover {
    border-color: rgba(6, 182, 212, 0.5);
}

.modern-stat-card:nth-child(3):hover {
    border-color: rgba(34, 197, 94, 0.5);
}

.modern-stat-card:nth-child(4):hover {
    border-color: rgba(249, 115, 22, 0.5);
}

.modern-stat-number {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.modern-stat-number-purple {
    background: linear-gradient(to right, #a855f7, #ec4899);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.modern-stat-number-cyan {
    background: linear-gradient(to right, #06b6d4, #3b82f6);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.modern-stat-number-green {
    background: linear-gradient(to right, #22c55e, #10b981);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.modern-stat-number-orange {
    background: linear-gradient(to right, #f97316, #ef4444);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.modern-stat-label {
    font-size: 0.875rem;
    color: #9ca3af;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.modern-value-dot {
    width: 0.5rem;
    height: 0.5rem;
    background: linear-gradient(to right, #a855f7, #ec4899);
    border-radius: 50%;
}

/* Contact section styles */
.modern-contact-bg {
    position: relative;
    background-color: #000000;
    padding: 5rem 0;
    overflow: hidden;
}

@media (min-width: 1024px) {
    .modern-contact-bg {
        padding: 8rem 0;
    }
}

.modern-contact-grid {
    position: absolute;
    inset: 0;
    background-image:
        linear-gradient(rgba(255,255,255,0.01) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255,255,255,0.01) 1px, transparent 1px);
    background-size: 120px 120px;
}

.modern-contact-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 24rem;
    height: 24rem;
    background: linear-gradient(to right, rgba(168, 85, 247, 0.2), rgba(236, 72, 153, 0.2), rgba(6, 182, 212, 0.2));
    border-radius: 50%;
    filter: blur(3rem);
}

.modern-contact-card {
    background-color: rgba(17, 24, 39, 0.5);
    backdrop-filter: blur(0.375rem);
    border: 1px solid #374151;
    border-radius: 1.5rem;
    padding: 2rem;
    transition: all 0.5s ease;
}

@media (min-width: 1024px) {
    .modern-contact-card {
        padding: 3rem;
    }
}

.modern-contact-card:hover {
    border-color: rgba(168, 85, 247, 0.5);
}

.modern-contact-email {
    display: inline-flex;
    align-items: center;
    font-size: 1.125rem;
    font-weight: 700;
    transition: all 0.3s ease;
}

@media (min-width: 1024px) {
    .modern-contact-email {
        font-size: 1.25rem;
    }
}

.modern-contact-divider {
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, #374151, transparent);
}

.modern-social-icon {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.modern-social-icon-github {
    background: linear-gradient(to right, rgba(168, 85, 247, 0.2), rgba(236, 72, 153, 0.2));
}

.modern-social-icon-github:hover {
    background: linear-gradient(to right, rgba(168, 85, 247, 0.3), rgba(236, 72, 153, 0.3));
}

.modern-social-icon-linkedin {
    background: linear-gradient(to right, rgba(6, 182, 212, 0.2), rgba(59, 130, 246, 0.2));
}

.modern-social-icon-linkedin:hover {
    background: linear-gradient(to right, rgba(6, 182, 212, 0.3), rgba(59, 130, 246, 0.3));
}

.modern-social-icon-twitter {
    background: linear-gradient(to right, rgba(34, 197, 94, 0.2), rgba(16, 185, 129, 0.2));
}

.modern-social-icon-twitter:hover {
    background: linear-gradient(to right, rgba(34, 197, 94, 0.3), rgba(16, 185, 129, 0.3));
}

.modern-social-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
}

.modern-social-link:hover {
    background-color: rgba(31, 41, 55, 0.5);
}

/* Footer styles */
.modern-footer {
    background-color: #000000;
    border-top: 1px solid #374151;
    position: relative;
    overflow: hidden;
    padding: 1rem;
}

.modern-footer-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffffff;
}

@media (min-width: 640px) {
    .modern-footer-title {
        font-size: 1.875rem;
    }
}

.modern-footer-subtitle {
    font-size: 1.125rem;
}

.modern-footer-text {
    color: #9ca3af;
    font-size: 0.875rem;
}

.modern-footer-brand {
    color: #6b7280;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    justify-content: center;
}

.modern-footer-brand-name {
    font-weight: 600;
}

/* Navbar styles */
.modern-navbar {
    position: sticky;
    top: 0;
    z-index: 50;
    width: 100%;
    border-bottom: 1px solid #374151;
    background-color: rgba(3, 7, 18, 0.95);
    backdrop-filter: blur(8px);
    color: #ffffff;
}

.modern-navbar-container {
    height: 3.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1rem;
}

@media (min-width: 640px) {
    .modern-navbar-container {
        padding: 0 1.5rem;
    }
}

.modern-navbar-brand {
    font-weight: 700;
    letter-spacing: -0.025em;
    text-decoration: none;
    color: #ffffff;
}

.modern-navbar-nav {
    display: none;
    gap: 1.5rem;
    align-items: center;
}

@media (min-width: 768px) {
    .modern-navbar-nav {
        display: flex;
    }
}

.modern-navbar-link {
    font-size: 0.875rem;
    font-weight: 500;
    color: #ffffff;
    text-decoration: none;
    transition: color 0.3s ease;
}

.modern-navbar-link:hover {
    color: #a855f7;
}

/* Mobile menu styles */
.modern-mobile-menu {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    font-size: 1.125rem;
    font-weight: 500;
    margin-top: 2rem;
    align-items: center;
    text-align: center;
}

.modern-mobile-menu-link {
    font-size: 1.125rem;
    font-weight: 500;
    color: #ffffff;
    text-decoration: none;
    transition: color 0.3s ease;
}

.modern-mobile-menu-link:hover {
    color: #a855f7;
}

/* Responsive utilities */
.modern-section {
    padding: 5rem 0;
}

@media (min-width: 1024px) {
    .modern-section {
        padding: 8rem 0;
    }
}

/* Animation utilities */
.modern-fade-in {
    animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Ensure proper text rendering */
.modern-theme * {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
